#ifndef MRMREADER_H
#define MRMREADER_H

#include <QObject>
#include <QVector>
#include <QString>
#include "filedata.h"
#include "../GlobalDefine.h"

// MRM数据结构体
struct StructMRM {
    uint Experiment;
    QString ID;
    double Q1;
    double Q3;
    double RT;
};

class MRMReader : public QObject
{
    Q_OBJECT

public:
    explicit MRMReader(QObject *parent = nullptr);
    ~MRMReader();

    // 获取当前扫描类型
    GlobalDefine::ScanMode getCurrentScanMode(FileData &data);
    
    // 判断是否为MRM数据
    bool isMRMData(FileData &data);
    
    // 获取MRM列表数据
    QVector<StructMRM> getMRMDataList(FileData &data);

private:
    // MRM数据列表
    QVector<StructMRM> Struct_MRM_Vec;
};

#endif // MRMREADER_H
