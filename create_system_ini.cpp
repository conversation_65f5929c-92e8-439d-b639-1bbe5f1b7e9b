#include <QCoreApplication>
#include <QSettings>
#include <QDebug>
#include <QFileInfo>
#include <QDir>

/**
 * @brief 创建system.ini配置文件来解决Period限制问题
 * 
 * 这个程序会在应用程序目录创建system.ini文件，
 * 设置Period为一个很大的值（1000天），从而解决数据截断问题
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 创建system.ini配置文件 ===";
    
    // 获取应用程序目录
    QString appDir = QCoreApplication::applicationDirPath();
    QString configPath = appDir + "/system.ini";
    
    qDebug() << "应用程序目录:" << appDir;
    qDebug() << "配置文件路径:" << configPath;
    
    // 创建配置文件
    QSettings settings(configPath, QSettings::IniFormat);
    
    // 设置Analysis节的参数
    settings.beginGroup("Analysis");
    settings.setValue("Period", 86400000);        // 1000天 = 86400 * 1000 秒
    settings.setValue("maxiHeighMassChart", 16777215);
    settings.endGroup();
    
    // 确保写入文件
    settings.sync();
    
    // 验证文件是否创建成功
    QFileInfo configFileInfo(configPath);
    if (configFileInfo.exists()) {
        qDebug() << "✅ system.ini文件创建成功！";
        qDebug() << "📄 文件大小:" << configFileInfo.size() << "字节";
        qDebug() << "📊 Period设置为:" << settings.value("Analysis/Period").toDouble() << "秒";
        qDebug() << "📊 相当于:" << settings.value("Analysis/Period").toDouble() / 86400.0 << "天";
        
        // 读取并显示文件内容
        qDebug() << "\n=== 配置文件内容 ===";
        QSettings readSettings(configPath, QSettings::IniFormat);
        QStringList groups = readSettings.childGroups();
        for (const QString &group : groups) {
            qDebug() << "[" << group << "]";
            readSettings.beginGroup(group);
            QStringList keys = readSettings.childKeys();
            for (const QString &key : keys) {
                QVariant value = readSettings.value(key);
                qDebug() << key << "=" << value.toString();
            }
            readSettings.endGroup();
        }
        
        qDebug() << "\n=== 使用说明 ===";
        qDebug() << "1. 配置文件已创建，现在可以运行主程序";
        qDebug() << "2. 主程序会自动读取这个配置文件";
        qDebug() << "3. Period值已设置为1000天，应该能解决数据截断问题";
        qDebug() << "4. 如果仍有问题，可以尝试使用新的LxCustomDataReader";
        
        return 0;
    } else {
        qDebug() << "❌ system.ini文件创建失败！";
        return -1;
    }
}
