#include "lxcustomdatareader.h"
#include <QMutableMapIterator>
#include "FileData/avgmassmanager.h"

LxCustomDataReader::LxCustomDataReader(QObject *parent) : QObject(parent)
{
    qDebug() << "LxCustomDataReader: 初始化自定义数据读取器";
}

LxCustomDataReader::~LxCustomDataReader()
{
    qDebug() << "LxCustomDataReader: 析构函数";
}

bool LxCustomDataReader::loadTICDataComplete(FileData *data)
{
    if (!data)
    {
        qDebug() << "LxCustomDataReader: FileData指针为空";
        return false;
    }

    QString filePath = data->getFilePath();
    if (filePath.isEmpty())
    {
        qDebug() << "LxCustomDataReader: 文件路径为空";
        return false;
    }

    // qDebug() << "LxCustomDataReader: 开始读取文件" << filePath;

    // 1. 读取文件头
    QByteArray streamHead;
    if (!readFileHeader(filePath, streamHead))
    {
        qDebug() << "LxCustomDataReader: 读取文件头失败";
        return false;
    }

    // 2. 解析文件头，获取XIC和其他数据线信息
    int numXIC = 0, numOtherLine = 0;
    QMap<quint32, QMap<QString, XICParam *>> xicMap;
    if (!parseStreamHead(streamHead, numXIC, numOtherLine, xicMap))
    {
        qDebug() << "LxCustomDataReader: 解析文件头失败";
        return false;
    }

    // qDebug() << "LxCustomDataReader: XIC数量:" << numXIC << ", 其他数据线数量:" << numOtherLine;

    // 3. 读取所有TIC数据（无Period限制）
    std::vector<qint64> indexArray;
    std::vector<double> ticX, ticY;
    QList<std::vector<double>> structLines;

    if (!readAllTICData(filePath, streamHead, numXIC, numOtherLine,
                        indexArray, ticX, ticY, xicMap, structLines))
    {
        qDebug() << "LxCustomDataReader: 读取TIC数据失败";
        return false;
    }

    // qDebug() << "LxCustomDataReader: 成功读取" << ticX.size() << "个数据点";

    // 4. 将数据填充到FileData结构中
    // 创建默认的TIC数据（事件ID为0）
    int eventId = 0;
    TicChartData *ticData = data->getTicData(eventId);
    if (!ticData)
    {
        ticData = data->createTicData(eventId);
        // qDebug() << "LxCustomDataReader: 创建TIC数据，事件ID:" << eventId;
    }

    // 填充TIC数据
    ticData->clearDataThreadSafe();
    for (size_t i = 0; i < ticX.size() && i < ticY.size(); ++i)
    {
        ticData->appendDataPointThreadSafe(ticX[i], ticY[i]);
    }

    // 设置索引数组（用于后续MASS数据读取）
    data->setIndexArray(indexArray);

    // 重要：解析StreamHead中的Segment信息
    if (!parseSegmentFromStreamHead(streamHead, data))
    {
        qDebug() << "LxCustomDataReader: 解析Segment信息失败";
        return false;
    }

    // 重要：填充timePoints和frameIndices数组，这些是MASS加载逻辑必需的
    data->timePoints.clear();
    data->frameIndices.clear();
    data->timePoints.reserve(ticX.size());
    data->frameIndices.reserve(indexArray.size());

    for (size_t i = 0; i < ticX.size(); ++i)
    {
        data->timePoints.append(ticX[i]);
    }

    for (size_t i = 0; i < indexArray.size(); ++i)
    {
        data->frameIndices.append(indexArray[i]);
    }

    // qDebug() << "LxCustomDataReader: 填充timePoints数组，大小:" << data->timePoints.size();
    // qDebug() << "LxCustomDataReader: 填充frameIndices数组，大小:" << data->frameIndices.size();

    // 处理XIC数据
    if (!xicMap.isEmpty())
    {
        QMutableMapIterator<quint32, QMap<QString, XICParam *>> eventIter(xicMap);
        while (eventIter.hasNext())
        {
            auto eventEntry = eventIter.next();
            quint32 xicEventId = eventEntry.key();

            QMutableMapIterator<QString, XICParam *> massIter(eventEntry.value());
            while (massIter.hasNext())
            {
                auto massEntry = massIter.next();
                QString massStr = massEntry.key();
                XICParam *xicParam = massEntry.value();

                if (xicParam && !xicParam->yListXIC.empty())
                {
                    // 获取或创建对应的TIC数据
                    TicChartData *xicTicData = data->getTicData(xicEventId);
                    if (!xicTicData)
                    {
                        xicTicData = data->createTicData(xicEventId);
                    }

                    // 创建XIC数据并获取UUID
                    QUuid xicUuid = xicTicData->createXicData();
                    XicChartData *xicData = xicTicData->getXicData(xicUuid);

                    if (xicData)
                    {
                        // 设置XIC关联的TIC事件ID
                        xicData->setTic_event_id(xicEventId);
                        xicData->setTitle(QString("XIC_m/z_%1").arg(massStr));

                        // 填充XIC数据点
                        QVector<QPointF> xicPoints;
                        xicPoints.reserve(ticX.size());
                        for (size_t i = 0; i < ticX.size() && i < xicParam->yListXIC.size(); ++i)
                        {
                            xicPoints.append(QPointF(ticX[i], xicParam->yListXIC[i]));
                        }

                        // 计算数据范围
                        if (!xicPoints.isEmpty())
                        {
                            double minX = xicPoints.first().x();
                            double maxX = xicPoints.first().x();
                            double minY = xicPoints.first().y();
                            double maxY = xicPoints.first().y();

                            for (const QPointF &point : xicPoints)
                            {
                                minX = qMin(minX, point.x());
                                maxX = qMax(maxX, point.x());
                                minY = qMin(minY, point.y());
                                maxY = qMax(maxY, point.y());
                            }

                            // 使用setXicDataComplete方法一次性设置数据和范围
                            xicData->setXicDataComplete(xicPoints, minX, maxX, minY, maxY,
                                                        QString("XIC_m/z_%1").arg(massStr));

                            qDebug() << "LxCustomDataReader: XIC数据设置完成";
                            qDebug() << "   事件ID:" << xicEventId << ", m/z:" << massStr;
                            qDebug() << "   数据点数:" << xicPoints.size();
                            qDebug() << "   X范围:" << minX << "~" << maxX;
                            qDebug() << "   Y范围:" << minY << "~" << maxY;
                        }
                        else
                        {
                            qDebug() << "LxCustomDataReader: XIC数据为空，事件ID:" << xicEventId << ", m/z:" << massStr;
                        }
                    }

                    qDebug() << "LxCustomDataReader: 添加XIC数据，事件ID:" << xicEventId
                             << ", m/z:" << massStr << ", 数据点数:" << xicParam->yListXIC.size();
                }
            }
        }
    }

    qDebug() << "LxCustomDataReader: TIC数据读取完成，总数据点:" << ticX.size();
    return true;
}

bool LxCustomDataReader::readFileHeader(const QString &filePath, QByteArray &streamHead)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << "LxCustomDataReader: 无法打开文件" << filePath;
        return false;
    }

    // 先读取固定大小的头部
    qint64 headerSize = sizeof(StreamHead);
    streamHead.resize(headerSize);

    if (file.read(streamHead.data(), headerSize) != headerSize)
    {
        qDebug() << "LxCustomDataReader: 读取文件头失败";
        file.close();
        return false;
    }

    // 获取实际头部长度并重新读取
    StreamHead *header = reinterpret_cast<StreamHead *>(streamHead.data());
    qint64 actualHeaderSize = header->length;

    if (actualHeaderSize > headerSize)
    {
        streamHead.resize(actualHeaderSize);
        file.seek(0);
        if (file.read(streamHead.data(), actualHeaderSize) != actualHeaderSize)
        {
            qDebug() << "LxCustomDataReader: 读取完整文件头失败";
            file.close();
            return false;
        }
    }

    file.close();

    if (m_debugMode)
    {
        debugStreamHead(reinterpret_cast<StreamHead *>(streamHead.data()));
    }

    return true;
}

bool LxCustomDataReader::parseStreamHead(const QByteArray &streamHead,
                                         int &numXIC, int &numOtherLine,
                                         QMap<quint32, QMap<QString, XICParam *>> &xicMap)
{
    numXIC = 0;
    numOtherLine = 0;
    xicMap.clear();

    // 解析XIC参数
    QString xicString = extractXICString(streamHead);
    if (!xicString.isEmpty())
    {
        numXIC = parseXICString(xicString, xicMap);
        qDebug() << "LxCustomDataReader: 解析到" << numXIC << "个XIC参数";
    }

    // 解析其他数据线参数
    QString lineString = extractOtherLineString(streamHead);
    if (!lineString.isEmpty())
    {
        QList<std::vector<double>> structLines;
        numOtherLine = parseOtherLineString(lineString, structLines);
        qDebug() << "LxCustomDataReader: 解析到" << numOtherLine << "个其他数据线";
    }

    return true;
}

QString LxCustomDataReader::extractXICString(const QByteArray &streamHead)
{
    int offset = sizeof(StreamHead);

    while (offset < streamHead.size())
    {
        if (offset + sizeof(StreamHeadParam) > streamHead.size())
        {
            break;
        }

        const StreamHeadParam *param = reinterpret_cast<const StreamHeadParam *>(
            streamHead.data() + offset);

        if (param->type == StreamHeadParam::Type_XIC_Param)
        {
            int dataSize = param->length - sizeof(StreamHeadParam);
            if (dataSize > 0 && offset + param->length <= streamHead.size())
            {
                QByteArray xicData(param->param, dataSize);
                return QString::fromUtf8(xicData);
            }
        }

        offset += param->length;
    }

    return QString();
}

QString LxCustomDataReader::extractOtherLineString(const QByteArray &streamHead)
{
    int offset = sizeof(StreamHead);

    while (offset < streamHead.size())
    {
        if (offset + sizeof(StreamHeadParam) > streamHead.size())
        {
            break;
        }

        const StreamHeadParam *param = reinterpret_cast<const StreamHeadParam *>(
            streamHead.data() + offset);

        if (param->type == StreamHeadParam::Type_Line_Param)
        {
            int dataSize = param->length - sizeof(StreamHeadParam);
            if (dataSize > 0 && offset + param->length <= streamHead.size())
            {
                QByteArray lineData(param->param, dataSize);
                return QString::fromUtf8(lineData);
            }
        }

        offset += param->length;
    }

    return QString();
}

int LxCustomDataReader::parseXICString(const QString &xicString,
                                       QMap<quint32, QMap<QString, XICParam *>> &xicMap)
{
    if (xicString.isEmpty())
    {
        return 0;
    }

    QStringList parts = xicString.split('/');
    if (parts.size() < 2)
    {
        return 0;
    }

    double massRange = parts[0].toDouble();
    QStringList curves = parts[1].split('@');

    // 清理现有数据
    for (auto &eventMap : xicMap)
    {
        for (auto *xicParam : eventMap)
        {
            delete xicParam;
        }
    }
    xicMap.clear();

    // 解析每个XIC曲线
    for (const QString &curve : curves)
    {
        QStringList curveInfo = curve.split(':');
        if (curveInfo.size() < 5)
        {
            continue;
        }

        quint32 eventId = curveInfo[0].toUInt();
        QString massStr = curveInfo[1];
        quint32 color = curveInfo[2].toUInt(nullptr, 16);
        double gain = curveInfo[3].toDouble();
        double offset = curveInfo[4].toDouble();

        xicMap[eventId][massStr] = new XICParam(massRange, color, gain, offset);
    }

    return curves.size();
}

bool LxCustomDataReader::readAllTICData(const QString &filePath, const QByteArray &streamHead,
                                        int numXIC, int numOtherLine,
                                        std::vector<qint64> &indexArray,
                                        std::vector<double> &ticX,
                                        std::vector<double> &ticY,
                                        QMap<quint32, QMap<QString, XICParam *>> &xicMap,
                                        QList<std::vector<double>> &structLines)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << "LxCustomDataReader: 无法打开文件进行数据读取" << filePath;
        return false;
    }

    qint64 headerSize = streamHead.size();
    qint64 dataSize = file.size() - headerSize;

    if (dataSize <= 0)
    {
        qDebug() << "LxCustomDataReader: 文件中没有数据";
        indexArray.clear();
        ticX.clear();
        ticY.clear();
        file.close();
        return true;
    }

    // 计算每行数据的大小
    int sizeI64 = sizeof(qint64);
    int sizeD = sizeof(double);
    int offsetI = sizeI64 + sizeD * 2 + sizeD * numXIC + sizeD * numOtherLine;

    qint64 lineCount = dataSize / offsetI;
    // qDebug() << "LxCustomDataReader: 数据大小:" << dataSize << ", 每行大小:" << offsetI << ", 总行数:" << lineCount;

    if (lineCount <= 0)
    {
        qDebug() << "LxCustomDataReader: 计算的行数无效";
        file.close();
        return false;
    }

    // 读取所有数据（关键：这里不使用Period限制）
    QByteArray allData;
    allData.resize(dataSize);

    file.seek(headerSize);
    if (file.read(allData.data(), dataSize) != dataSize)
    {
        qDebug() << "LxCustomDataReader: 读取数据失败";
        file.close();
        return false;
    }
    file.close();

    // 预分配内存
    indexArray.resize(lineCount);
    ticX.resize(lineCount);
    ticY.resize(lineCount);

    // 初始化其他数据线
    structLines.clear();
    for (int j = 0; j < numOtherLine; ++j)
    {
        structLines.append(std::vector<double>(lineCount));
    }

    // 初始化XIC数据
    for (auto &eventMap : xicMap)
    {
        for (auto *xicParam : eventMap)
        {
            if (xicParam)
            {
                xicParam->yListXIC.resize(lineCount);
            }
        }
    }

    // 解析数据
    int offsetY = sizeI64 + sizeD;
    int offsetXIC = sizeI64 + sizeD + sizeD;
    int offsetOtherLine = offsetXIC + numXIC * sizeD;

    for (qint64 i = 0; i < lineCount; ++i)
    {
        int lineOffset = i * offsetI;

        // 读取索引、时间和TIC强度
        memcpy(&indexArray[i], allData.data() + lineOffset, sizeI64);
        memcpy(&ticX[i], allData.data() + lineOffset + sizeI64, sizeD);
        memcpy(&ticY[i], allData.data() + lineOffset + offsetY, sizeD);

        // 读取XIC数据
        if (!xicMap.isEmpty())
        {
            int xicIndex = 0;
            QMutableMapIterator<quint32, QMap<QString, XICParam *>> eventIter(xicMap);
            while (eventIter.hasNext())
            {
                QMutableMapIterator<QString, XICParam *> massIter(eventIter.next().value());
                while (massIter.hasNext())
                {
                    XICParam *xicParam = massIter.next().value();
                    if (xicParam && xicIndex < numXIC)
                    {
                        memcpy(&xicParam->yListXIC[i],
                               allData.data() + lineOffset + offsetXIC + sizeD * xicIndex, sizeD);
                        ++xicIndex;
                    }
                }
            }
        }

        // 读取其他数据线
        for (int j = 0; j < numOtherLine; ++j)
        {
            memcpy(&structLines[j][i],
                   allData.data() + lineOffset + offsetOtherLine + sizeD * j, sizeD);
        }
    }

    // qDebug() << "LxCustomDataReader: 成功读取所有数据，共" << lineCount << "行";
    return true;
}

int LxCustomDataReader::parseOtherLineString(const QString &lineString,
                                             QList<std::vector<double>> &structLines)
{
    structLines.clear();
    if (lineString.isEmpty())
    {
        return 0;
    }

    int totalCount = 0;
    QStringList parts = lineString.split(';');

    for (const QString &part : parts)
    {
        if (part.isEmpty())
            continue;

        QStringList lineInfo = part.split('&');
        if (lineInfo.size() < 2)
            continue;

        int lineCount = lineInfo[1].toInt();
        if (lineCount < 1)
            continue;

        totalCount += lineCount;
        structLines.append(std::vector<double>(0));
    }

    return totalCount;
}

void LxCustomDataReader::debugStreamHead(const StreamHead *streamHead)
{
    if (!streamHead)
        return;

    // qDebug() << "LxCustomDataReader: 文件头信息:";
    // qDebug() << "  长度:" << streamHead->length;
    // qDebug() << "  类型:" << streamHead->typeParam;
    // qDebug() << "  时间戳:" << streamHead->dateTime;

    // QDateTime dt = QDateTime::fromSecsSinceEpoch(streamHead->dateTime, Qt::UTC);
    // qDebug() << "  本地时间:" << dt.toLocalTime().toString("yyyy-MM-dd hh:mm:ss");
}

bool LxCustomDataReader::loadMassData(int index, const std::vector<qint64> &indexArray,
                                      QByteArray &dataY, QByteArray &streamBody,
                                      const QString &filePath)
{
    if (indexArray.size() <= index)
    {
        qDebug() << "LxCustomDataReader: 索引超出范围" << index << "/" << indexArray.size();
        return false;
    }

    QString datFilePath = filePath;
    if (datFilePath.endsWith(".Param"))
    {
        datFilePath.replace(".Param", ".Dat");
    }
    else if (datFilePath.endsWith(".P"))
    {
        datFilePath.replace(".P", ".D");
    }

    return readMassDataBody(datFilePath, indexArray, index, index, dataY, streamBody);
}

bool LxCustomDataReader::readMassDataBody(const QString &filePath,
                                          const std::vector<qint64> &indexArray,
                                          int frameBegin, int frameEnd,
                                          QByteArray &dataY, QByteArray &streamBody)
{
    if (indexArray.size() <= frameBegin || indexArray.size() <= frameEnd)
    {
        qDebug() << "LxCustomDataReader: 帧索引超出范围";
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        qDebug() << "LxCustomDataReader: 无法打开MASS数据文件" << filePath;
        return false;
    }

    qint64 fileSize = file.size();
    // qDebug() << "📁 文件信息:";
    // qDebug() << "   文件路径:" << filePath;
    // qDebug() << "   文件大小:" << fileSize << "字节 (" << (fileSize / 1024.0 / 1024.0) << "MB)";
    // qDebug() << "   请求帧范围:" << frameBegin << "~" << frameEnd;

    int accCount = 0;
    QByteArray uncompressBuffer;

    for (int currentFrame = frameBegin; currentFrame <= frameEnd; ++currentFrame)
    {
        qint64 offset = indexArray[currentFrame];
        // qDebug() << "🔍 帧" << currentFrame << "信息:";
        // qDebug() << "   索引数组值:" << offset;
        // qDebug() << "   文件大小:" << fileSize;
        // qDebug() << "   偏移量是否有效:" << (offset < fileSize ? "是" : "否");

        if (offset >= fileSize)
        {
            qDebug() << "❌ LxCustomDataReader: 偏移量" << offset << "超出文件大小" << fileSize;
            file.close();
            return false;
        }

        // 读取StreamBody头部
        qint64 streamBodySize = sizeof(StreamBody);
        streamBody.resize(streamBodySize);

        if (!file.seek(offset))
        {
            qDebug() << "LxCustomDataReader: 文件定位失败";
            file.close();
            return false;
        }

        if (file.read(streamBody.data(), streamBodySize) != streamBodySize)
        {
            qDebug() << "LxCustomDataReader: 读取StreamBody头部失败";
            file.close();
            return false;
        }

        StreamBody *streamBodyHeader = reinterpret_cast<StreamBody *>(streamBody.data());

        // 读取参数部分（如果有）
        if (streamBodyHeader->lengthParam > 0)
        {
            quint32 paramLength = streamBodyHeader->lengthParam;
            streamBody.resize(streamBodySize + paramLength);
            if (file.read(streamBody.data() + streamBodySize, paramLength) != paramLength)
            {
                qDebug() << "LxCustomDataReader: 读取参数部分失败";
                file.close();
                return false;
            }
            streamBodyHeader = reinterpret_cast<StreamBody *>(streamBody.data());
        }

        // 读取数据部分
        qint64 dataSize = streamBodyHeader->length - streamBodySize - streamBodyHeader->lengthParam;
        QByteArray rawData;
        rawData.resize(dataSize);

        if (file.read(rawData.data(), dataSize) != dataSize)
        {
            qDebug() << "LxCustomDataReader: 读取数据部分失败";
            file.close();
            return false;
        }

        // 根据数据类型处理数据
        QByteArray processedData;
        switch (streamBodyHeader->typeParam)
        {
        case StreamBody::Type_Uint16Compress:
            processedData = qUncompress(rawData);
            convertDataType<quint16>(processedData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_Uint16:
            convertDataType<quint16>(rawData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_FloatCompress:
            processedData = qUncompress(rawData);
            convertDataType<float>(processedData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_Float:
            convertDataType<float>(rawData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_DoubleCompress:
            processedData = qUncompress(rawData);
            convertDataType<double>(processedData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_Double:
            convertDataType<double>(rawData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_Uint8Compress:
            processedData = qUncompress(rawData);
            convertDataType<quint8>(processedData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_Uint8:
            convertDataType<quint8>(rawData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_Uint32Compress:
            processedData = qUncompress(rawData);
            convertDataType<quint32>(processedData, dataY, accCount, frameBegin, frameEnd);
            break;
        case StreamBody::Type_Uint32:
            convertDataType<quint32>(rawData, dataY, accCount, frameBegin, frameEnd);
            break;
        default:
            qDebug() << "LxCustomDataReader: 未知的数据类型" << streamBodyHeader->typeParam;
            break;
        }

        ++accCount;
    }

    file.close();
    return true;
}

template <typename T>
void LxCustomDataReader::convertDataType(const QByteArray &srcData, QByteArray &dstData,
                                         int countACC, int frameBegin, int frameEnd)
{
    qint64 dataCount = srcData.size() / sizeof(T);
    const T *srcPtr = reinterpret_cast<const T *>(srcData.data());

    if (countACC == 0)
    {
        // 第一帧数据，直接转换为double
        dstData.resize(dataCount * sizeof(double));
        double *dstPtr = reinterpret_cast<double *>(dstData.data());

        for (qint64 i = 0; i < dataCount; ++i)
        {
            dstPtr[i] = static_cast<double>(srcPtr[i]);
        }
    }
    else if (countACC + frameBegin == frameEnd)
    {
        // 最后一帧，计算平均值
        double *dstPtr = reinterpret_cast<double *>(dstData.data());

        for (qint64 i = 0; i < dataCount; ++i)
        {
            dstPtr[i] = (static_cast<double>(countACC - 1) / static_cast<double>(countACC)) * dstPtr[i] +
                        static_cast<double>(srcPtr[i]) / static_cast<double>(countACC);
        }
    }
    else
    {
        // 中间帧，累积平均
        double *dstPtr = reinterpret_cast<double *>(dstData.data());

        for (qint64 i = 0; i < dataCount; ++i)
        {
            dstPtr[i] = (static_cast<double>(countACC - 1) / static_cast<double>(countACC)) * dstPtr[i] +
                        static_cast<double>(srcPtr[i]) / static_cast<double>(countACC);
        }
    }
}

bool LxCustomDataReader::loadMassDataComplete(FileData *data, int frameIndex, int eventId)
{
    if (!data)
    {
        qDebug() << "LxCustomDataReader::loadMassDataComplete: FileData指针为空";
        return false;
    }

    const std::vector<qint64> &indexArray = data->getIndexArray();
    if (indexArray.empty() || frameIndex < 0 || frameIndex >= indexArray.size())
    {
        qDebug() << "LxCustomDataReader::loadMassDataComplete: 无效的帧索引" << frameIndex;
        return false;
    }

    // 读取原始MASS数据
    QByteArray massData, streamBody;
    QString filePath = data->getFilePath();

    if (!loadMassData(frameIndex, indexArray, massData, streamBody, filePath))
    {
        qDebug() << "LxCustomDataReader::loadMassDataComplete: 读取原始MASS数据失败";
        return false;
    }

    // 解析StreamBody头部获取数据类型信息
    if (streamBody.size() < sizeof(StreamBody))
    {
        qDebug() << "LxCustomDataReader::loadMassDataComplete: StreamBody数据不完整";
        return false;
    }

    StreamBody *streamBodyHeader = reinterpret_cast<StreamBody *>(streamBody.data());

    // 将原始数据转换为double数组（质谱强度值）
    QVector<double> intensities;
    int dataPointCount = massData.size() / sizeof(double);

    if (dataPointCount > 0)
    {
        const double *dataPtr = reinterpret_cast<const double *>(massData.data());
        intensities.reserve(dataPointCount);

        for (int i = 0; i < dataPointCount; ++i)
        {
            intensities.append(dataPtr[i]);
        }
    }

    // 严格按照要求：必须从Segment信息中获取真实的m/z轴，失败就失败
    // qDebug() << "LxCustomDataReader::loadMassDataComplete: 开始解析真实的m/z轴";
    // qDebug() << "   原始数据点数:" << dataPointCount;
    // qDebug() << "   强度数据范围:" << *std::min_element(intensities.begin(), intensities.end())
    //          << "~" << *std::max_element(intensities.begin(), intensities.end());

    // 检查FileData是否有Segment信息
    if (data->mSegment.empty())
    {
        qDebug() << "LxCustomDataReader::loadMassDataComplete: 没有Segment信息";
        qDebug() << "   无法获取真实的m/z轴，操作失败";
        return false;
    }

    // 解析第一个Segment的QByteArray数据
    const QByteArray &segmentData = data->mSegment[0];
    if (segmentData.size() < sizeof(cParamValue::_Segment))
    {
        qDebug() << "LxCustomDataReader::loadMassDataComplete: Segment数据不完整";
        qDebug() << "   数据大小:" << segmentData.size() << ", 需要:" << sizeof(cParamValue::_Segment);
        qDebug() << "   无法获取真实的m/z轴，操作失败";
        return false;
    }

    // 将QByteArray转换为_Segment结构体
    const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segmentData.constData());

    if (pSegment->countsEvent == 0)
    {
        qDebug() << "LxCustomDataReader::loadMassDataComplete: Segment中没有事件";
        qDebug() << "   无法获取真实的m/z轴，操作失败";
        return false;
    }

    // 获取第一个事件的指针
    cParamValue::_Event *pEvent = (cParamValue::_Event *)((char *)&(pSegment->fisrtEvent));

    QVector<double> mzValues;
    mzValues.reserve(dataPointCount);

    // qDebug() << "LxCustomDataReader::loadMassDataComplete: 事件类型:" << pEvent->type;

    if (pEvent->type == cParamValue::Type_Scan)
    {
        // Scan事件：使用线性扫描范围
        cParamValue::_EventScan *pEventScan = (cParamValue::_EventScan *)pEvent;
        double startMass = pEventScan->msStart; // 正确的成员名称
        double endMass = pEventScan->msEnd;     // 正确的成员名称

        if (startMass > 0 && endMass > startMass)
        {
            double mzStep = (endMass - startMass) / (dataPointCount - 1);
            for (int i = 0; i < dataPointCount; ++i)
            {
                mzValues.append(startMass + i * mzStep);
            }
            // qDebug() << "LxCustomDataReader::loadMassDataComplete: Scan事件，m/z范围:" << startMass << "~" << endMass;
        }
        else
        {
            qDebug() << "LxCustomDataReader::loadMassDataComplete: Scan事件范围无效";
            qDebug() << "   起始质量:" << startMass << ", 结束质量:" << endMass;
            qDebug() << "   无法获取真实的m/z轴，操作失败";
            return false;
        }
    }
    else if (pEvent->type == cParamValue::Type_SIM || pEvent->type == cParamValue::Type_SIM_2048)
    {
        // SIM事件：使用mass数组中的真实质量值
        cParamValue::_EventSIM *pEventSIM = (cParamValue::_EventSIM *)pEvent;

        // 从mass数组中提取有效的质量值
        QVector<double> validMasses;
        for (int i = 0; i < qMin(dataPointCount, 2048); ++i)
        {
            if (pEventSIM->mass[i] > 0.0001) // 有效的质量值
            {
                validMasses.append(pEventSIM->mass[i]);
            }
        }

        if (validMasses.size() >= dataPointCount)
        {
            // 使用真实的质量值
            for (int i = 0; i < dataPointCount; ++i)
            {
                mzValues.append(validMasses[i]);
            }
            qDebug() << "LxCustomDataReader::loadMassDataComplete: SIM事件，使用真实质量值，数量:" << validMasses.size();
        }
        else
        {
            qDebug() << "LxCustomDataReader::loadMassDataComplete: SIM质量值不足";
            qDebug() << "   有效质量值数量:" << validMasses.size() << ", 需要:" << dataPointCount;
            qDebug() << "   无法获取真实的m/z轴，操作失败";
            return false;
        }
    }
    else
    {
        // qDebug() << "LxCustomDataReader::loadMassDataComplete: 未知事件类型:" << pEvent->type;
        // qDebug() << "   无法获取真实的m/z轴，操作失败";
        return false;
    }

    // 创建QPointF数组
    QVector<QPointF> massPoints;
    massPoints.reserve(dataPointCount);

    for (int i = 0; i < dataPointCount; ++i)
    {
        massPoints.append(QPointF(mzValues[i], intensities[i]));
    }

    // 获取或创建TicChartData
    TicChartData *ticData = data->getTicData(eventId);
    if (!ticData)
    {
        ticData = data->createTicData(eventId);
        if (!ticData)
        {
            qDebug() << "LxCustomDataReader::loadMassDataComplete: 无法创建TicChartData";
            return false;
        }
    }

    // 获取或创建MassChartData
    MassChartData *massChartData = ticData->getMassData();
    if (!massChartData)
    {
        massChartData = ticData->createMassData();
        if (!massChartData)
        {
            qDebug() << "LxCustomDataReader::loadMassDataComplete: 无法创建MassChartData";
            return false;
        }
    }

    // 设置MASS数据
    massChartData->setMassData(massPoints);

    // qDebug() << "LxCustomDataReader::loadMassDataComplete: 成功设置MASS数据";
    // qDebug() << "   数据点数:" << massPoints.size();
    // qDebug() << "   m/z范围:" << mzValues.first() << "~" << mzValues.last();
    // qDebug() << "   强度范围:" << *std::min_element(intensities.begin(), intensities.end())
    //          << "~" << *std::max_element(intensities.begin(), intensities.end());

    return true;

    for (int i = 0; i < dataPointCount; ++i)
    {
        massPoints.append(QPointF(mzValues[i], intensities[i]));
    }

    // 计算数据范围
    if (!massPoints.isEmpty())
    {
        double minX = mzValues.first();
        double maxX = mzValues.last();

        auto minMaxY = std::minmax_element(intensities.begin(), intensities.end());
        double minY = *minMaxY.first;
        double maxY = *minMaxY.second;

        // 获取或创建对应的TIC数据
        TicChartData *ticData = data->getTicData(eventId);
        if (!ticData)
        {
            ticData = data->createTicData(eventId);
        }

        // 创建MASS数据
        MassChartData *massChartData = ticData->createMassData();
        if (massChartData)
        {
            // 设置MASS数据
            massChartData->setMassDataComplete(massPoints, minX, maxX, minY, maxY,
                                               QString("MASS数据_帧%1").arg(frameIndex));

            qDebug() << "LxCustomDataReader::loadMassDataComplete: 成功设置MASS数据";
            qDebug() << "   数据点数:" << massPoints.size();
            qDebug() << "   m/z范围:" << minX << "~" << maxX;
            qDebug() << "   强度范围:" << minY << "~" << maxY;

            return true;
        }
        else
        {
            qDebug() << "LxCustomDataReader::loadMassDataComplete: 创建MassChartData失败";
        }
    }

    return false;
}

QPair<double, double> LxCustomDataReader::parseMzRangeFromStreamBody(const QByteArray &streamBody)
{
    if (streamBody.size() < sizeof(StreamBody))
    {
        qDebug() << "LxCustomDataReader::parseMzRangeFromStreamBody: StreamBody数据不完整";
        return qMakePair(0.0, 0.0); // 返回无效范围
    }

    const StreamBody *streamBodyHeader = reinterpret_cast<const StreamBody *>(streamBody.constData());

    // 如果有扩展参数，解析m/z范围
    if (streamBodyHeader->lengthParam > 0)
    {
        qDebug() << "LxCustomDataReader::parseMzRangeFromStreamBody: 解析扩展参数";
        qDebug() << "   扩展参数长度:" << streamBodyHeader->lengthParam;
        qDebug() << "   数据类型:" << streamBodyHeader->typeParam;

        // 扩展参数位于StreamBody之后
        const char *extParams = streamBody.constData() + sizeof(StreamBody);
        int extParamSize = streamBodyHeader->lengthParam;

        qDebug() << "   扩展参数详细信息:";
        qDebug() << "   - 参数大小:" << extParamSize << "字节";

        // 输出扩展参数的原始字节数据（前64字节）
        QString hexData;
        for (int i = 0; i < qMin(extParamSize, 64); ++i)
        {
            hexData += QString("%1 ").arg((unsigned char)extParams[i], 2, 16, QChar('0'));
            if ((i + 1) % 16 == 0)
                hexData += "\n   ";
        }
        qDebug() << "   - 原始数据(前64字节):\n  " << hexData;

        // 尝试多种可能的解析方式
        if (extParamSize >= 16)
        {
            // 方式1：前两个double值
            double minMz1 = *reinterpret_cast<const double *>(extParams);
            double maxMz1 = *reinterpret_cast<const double *>(extParams + 8);
            qDebug() << "   - 方式1(前16字节):" << minMz1 << "~" << maxMz1;

            // 方式2：跳过一些字节后的double值
            if (extParamSize >= 32)
            {
                double minMz2 = *reinterpret_cast<const double *>(extParams + 16);
                double maxMz2 = *reinterpret_cast<const double *>(extParams + 24);
                qDebug() << "   - 方式2(16-32字节):" << minMz2 << "~" << maxMz2;
            }

            // 方式3：作为float值读取
            if (extParamSize >= 8)
            {
                float minMzF = *reinterpret_cast<const float *>(extParams);
                float maxMzF = *reinterpret_cast<const float *>(extParams + 4);
                qDebug() << "   - 方式3(float):" << minMzF << "~" << maxMzF;
            }

            // 验证方式1的合理性
            if (minMz1 > 0 && maxMz1 > minMz1 && maxMz1 < 100000)
            {
                qDebug() << "   ✅ 使用方式1的m/z范围:" << minMz1 << "~" << maxMz1;
                return qMakePair(minMz1, maxMz1);
            }
        }

        qDebug() << "   ⚠️ 无法从扩展参数解析有效的m/z范围";
    }

    qDebug() << "LxCustomDataReader::parseMzRangeFromStreamBody: 没有扩展参数或解析失败";
    return qMakePair(0.0, 0.0); // 返回无效范围，让调用者处理
}

bool LxCustomDataReader::loadXicDataBatch(FileData *data, int eventId, double mz)
{
    if (!data)
    {
        qDebug() << "LxCustomDataReader::loadXicDataBatch: FileData指针为空";
        return false;
    }

    // qDebug() << "LxCustomDataReader::loadXicDataBatch: 开始加载XIC数据";
    // qDebug() << "   文件路径:" << data->getFilePath();
    // qDebug() << "   事件ID:" << eventId;
    // qDebug() << "   目标m/z:" << mz;

    // 获取对应的TIC数据
    TicChartData *ticData = data->getTicData(eventId);
    if (!ticData)
    {
        qDebug() << "LxCustomDataReader::loadXicDataBatch: 找不到对应的TIC数据，事件ID:" << eventId;
        return false;
    }

    // 检查是否已有XIC数据（暂时注释掉，允许重复加载用于调试）
    if (ticData->hasXicData())
    {
        qDebug() << "LxCustomDataReader::loadXicDataBatch: 该TIC已有XIC数据，但继续加载新的XIC";
        // return true; // 暂时注释掉，允许创建多个XIC用于调试
    }

    // 创建XIC数据
    QUuid xicUuid = ticData->createXicData();
    XicChartData *xicData = ticData->getXicData(xicUuid);

    if (!xicData)
    {
        qDebug() << "LxCustomDataReader::loadXicDataBatch: 创建XIC数据失败";
        return false;
    }

    // 设置XIC基本信息
    xicData->setTic_event_id(eventId);
    // 不在这里设置标题，使用TicChartData::createXicData中设置的标题

    // 获取TIC的时间轴数据
    QVector<double> ticTimeData = ticData->getDataX();
    if (ticTimeData.isEmpty())
    {
        qDebug() << "LxCustomDataReader::loadXicDataBatch: TIC时间数据为空";
        return false;
    }

    qDebug() << "LxCustomDataReader::loadXicDataBatch: TIC时间数据点数:" << ticTimeData.size();
    if (ticTimeData.size() > 0)
    {
        qDebug() << "   第一个时间点:" << ticTimeData.first();
        qDebug() << "   最后一个时间点:" << ticTimeData.last();
    }

    // 从实际质谱数据中提取指定m/z的强度
    QVector<QPointF> xicPoints;
    xicPoints.reserve(ticTimeData.size());

    qDebug() << "LxCustomDataReader::loadXicDataBatch: 开始从质谱数据提取XIC";
    qDebug() << "   目标m/z:" << mz << ", 容差: ±0.5";

    const std::vector<qint64> &indexArray = data->getIndexArray();
    QString datFilePath = data->getFilePath().replace(".Param", ".dat");

    double mzTolerance = 0.5; // m/z容差
    double minY = std::numeric_limits<double>::max();
    double maxY = std::numeric_limits<double>::lowest();

    // 遍历每个时间点，提取对应的质谱数据
    for (int timeIndex = 0; timeIndex < ticTimeData.size() && timeIndex < indexArray.size(); ++timeIndex)
    {
        double timePoint = ticTimeData[timeIndex];
        double extractedIntensity = 0.0;

        // 读取该时间点的质谱数据
        QByteArray massData, streamBody;
        if (loadMassData(timeIndex, indexArray, massData, streamBody, datFilePath))
        {
            // 解析质谱数据，查找目标m/z附近的强度
            int dataPointCount = massData.size() / sizeof(double);
            if (dataPointCount > 0)
            {
                const double *intensityPtr = reinterpret_cast<const double *>(massData.data());

                // 使用与MASS相同的方式从Segment获取真实的m/z范围
                QPair<double, double> mzRange = getMzRangeFromSegment(data, eventId);
                double minMz = mzRange.first;
                double maxMz = mzRange.second;

                // 如果无法获取有效范围，跳过这个时间点
                if (minMz <= 0 || maxMz <= minMz)
                {
                    qDebug() << "LxCustomDataReader::loadXicDataBatch: 无法获取有效的m/z范围，跳过时间点" << timePoint;
                    continue;
                }

                double mzStep = (maxMz - minMz) / (dataPointCount - 1);

                // 查找目标m/z对应的索引范围
                int targetIndex = static_cast<int>((mz - minMz) / mzStep);
                int searchRange = static_cast<int>(mzTolerance / mzStep) + 1;

                // 在容差范围内查找最大强度
                for (int i = qMax(0, targetIndex - searchRange);
                     i <= qMin(dataPointCount - 1, targetIndex + searchRange); ++i)
                {
                    extractedIntensity = qMax(extractedIntensity, intensityPtr[i]);
                }
            }
        }

        // 如果没有提取到数据，使用基线强度
        if (extractedIntensity == 0.0)
        {
            extractedIntensity = 1000.0; // 基线强度
        }

        xicPoints.append(QPointF(timePoint, extractedIntensity));
        minY = qMin(minY, extractedIntensity);
        maxY = qMax(maxY, extractedIntensity);
    }

    qDebug() << "LxCustomDataReader::loadXicDataBatch: XIC数据提取完成，数量:" << xicPoints.size();
    qDebug() << "   强度范围:" << minY << "~" << maxY;

    // 计算数据范围
    if (!xicPoints.isEmpty())
    {
        double minX = ticTimeData.first();
        double maxX = ticTimeData.last();

        // 使用setDataWithRange方法设置数据和范围，保持原有标题
        xicData->setDataWithRange(xicPoints, minX, maxX, minY, maxY);

        qDebug() << "LxCustomDataReader::loadXicDataBatch: XIC数据生成完成";
        qDebug() << "   数据点数:" << xicPoints.size();
        qDebug() << "   X范围:" << minX << "~" << maxX;
        qDebug() << "   Y范围:" << minY << "~" << maxY;

        // 验证XIC数据是否正确设置
        QVector<QPointF> verifyData = xicData->getData();
        qDebug() << "LxCustomDataReader::loadXicDataBatch: 验证XIC数据设置";
        qDebug() << "   设置后数据点数:" << verifyData.size();
        qDebug() << "   设置后X范围:" << xicData->getMinX() << "~" << xicData->getMaxX();
        qDebug() << "   设置后Y范围:" << xicData->getMinY() << "~" << xicData->getMaxY();

        if (verifyData.size() > 0)
        {
            qDebug() << "   第一个数据点:" << verifyData.first();
            qDebug() << "   最后一个数据点:" << verifyData.last();
        }

        return true;
    }
    else
    {
        qDebug() << "LxCustomDataReader::loadXicDataBatch: XIC数据生成失败";
        return false;
    }
}

bool LxCustomDataReader::loadMassDataForAvg(int eventId, const QVector<int> &frameIndexVec, FileData *data)
{
    if (!data)
    {
        qDebug() << "LxCustomDataReader::loadMassDataForAvg: FileData指针为空";
        return false;
    }

    if (frameIndexVec.isEmpty())
    {
        qDebug() << "LxCustomDataReader::loadMassDataForAvg: 帧索引列表为空";
        return false;
    }

    // qDebug() << "LxCustomDataReader::loadMassDataForAvg: 开始加载平均质谱数据";
    // qDebug() << "   事件ID:" << eventId;
    // qDebug() << "   帧索引数量:" << frameIndexVec.size();
    // qDebug() << "   文件路径:" << data->getFilePath();

    // 统一按照示例项目的标准方式读取数据，不区分版本

    QString filePath = data->getFilePath();

    // 检查文件是否存在
    if (!QFile::exists(filePath))
    {
        qDebug() << "LxCustomDataReader::loadMassDataForAvg: 文件不存在:" << filePath;
        return false;
    }

    // 获取索引数组
    const std::vector<qint64> &indexArray = data->frameIndices.toStdVector();
    if (indexArray.empty())
    {
        qDebug() << "LxCustomDataReader::loadMassDataForAvg: 索引数组为空，需要先加载TIC数据";
        return false;
    }

    qDebug() << "LxCustomDataReader::loadMassDataForAvg: 索引数组大小:" << indexArray.size();
    qDebug() << "LxCustomDataReader::loadMassDataForAvg: 请求的帧索引范围:"
             << *std::min_element(frameIndexVec.begin(), frameIndexVec.end())
             << "~" << *std::max_element(frameIndexVec.begin(), frameIndexVec.end());

    try
    {
        // 累积所有帧的质谱数据
        QVector<double> avgMassX, avgMassY;
        bool xAxisInitialized = false;
        int validFrameCount = 0;

        for (int frameIndex : frameIndexVec)
        {
            // 检查帧索引有效性
            if (frameIndex < 0 || frameIndex >= static_cast<int>(indexArray.size()))
            {
                qDebug() << "LxCustomDataReader::loadMassDataForAvg: 无效的帧索引:" << frameIndex
                         << "，有效范围: 0~" << (indexArray.size() - 1);
                continue;
            }

            // 检查索引数组中的偏移量是否有效
            qint64 fileOffset = indexArray[frameIndex];
            if (fileOffset <= 0)
            {
                qDebug() << "LxCustomDataReader::loadMassDataForAvg: 无效的文件偏移量:" << fileOffset
                         << "，帧索引:" << frameIndex;
                continue;
            }

            // 使用与单个MASS加载相同的方法读取数据
            QByteArray massData, streamBody;
            bool success = loadMassData(frameIndex, indexArray, massData, streamBody, filePath);

            if (!success)
            {
                qDebug() << "LxCustomDataReader::loadMassDataForAvg: 读取帧" << frameIndex << "失败";
                continue;
            }

            // 使用与loadMassDataComplete相同的解析逻辑
            QVector<double> frameY;
            int dataPointCount = massData.size() / sizeof(double);

            if (dataPointCount > 0)
            {
                const double *dataPtr = reinterpret_cast<const double *>(massData.data());
                frameY.reserve(dataPointCount);

                for (int i = 0; i < dataPointCount; ++i)
                {
                    frameY.append(dataPtr[i]);
                }
            }
            else
            {
                qDebug() << "LxCustomDataReader::loadMassDataForAvg: 帧" << frameIndex << "数据点数为0";
                continue;
            }

            // 初始化X轴数据（只需要一次）- 使用与loadMassDataComplete相同的逻辑
            if (!xAxisInitialized && !frameY.isEmpty())
            {
                // 生成标准的m/z轴（与单个MASS加载保持一致）
                avgMassX.clear();
                avgMassX.reserve(dataPointCount);

                // 从StreamBody获取真实的m/z范围（与单个MASS加载保持一致）
                QPair<double, double> mzRange = parseMzRangeFromStreamBody(streamBody);
                double startMz = mzRange.first;
                double endMz = mzRange.second;

                // 如果无法获取有效范围，无法初始化X轴
                if (startMz <= 0 || endMz <= startMz)
                {
                    qDebug() << "LxCustomDataReader::loadMassDataForAvg: 无法获取有效的m/z范围，无法初始化X轴";
                    qDebug() << "   读取到的范围:" << startMz << "~" << endMz;
                    qDebug() << "   跳过这一帧，尝试下一帧";
                    continue; // 跳过这一帧，尝试下一帧
                }

                qDebug() << "LxCustomDataReader::loadMassDataForAvg: 从扩展参数获取m/z范围:" << startMz << "~" << endMz;

                double stepMz = (endMz - startMz) / (dataPointCount - 1);

                for (int i = 0; i < dataPointCount; ++i)
                {
                    avgMassX.append(startMz + i * stepMz);
                }

                avgMassY.resize(dataPointCount);
                avgMassY.fill(0.0);
                xAxisInitialized = true;
                qDebug() << "LxCustomDataReader::loadMassDataForAvg: X轴初始化完成，数据点数:" << avgMassX.size();
            }

            // 累加Y轴数据
            if (xAxisInitialized && frameY.size() == avgMassY.size())
            {
                for (int i = 0; i < frameY.size(); ++i)
                {
                    avgMassY[i] += frameY[i];
                }
                validFrameCount++;
                qDebug() << "LxCustomDataReader::loadMassDataForAvg: 累加帧" << frameIndex << "完成";
            }
            else
            {
                qDebug() << "LxCustomDataReader::loadMassDataForAvg: 帧" << frameIndex << "数据大小不匹配，跳过";
            }
        }

        if (validFrameCount == 0)
        {
            qDebug() << "LxCustomDataReader::loadMassDataForAvg: 没有有效的帧数据";
            return false;
        }

        // 将累积数据存储到AvgMassManager
        AvgMassManager::setAvgMass(filePath, eventId, avgMassX, avgMassY, xAxisInitialized, validFrameCount);

        qDebug() << "LxCustomDataReader::loadMassDataForAvg: 平均质谱数据加载完成";
        qDebug() << "   有效帧数:" << validFrameCount;
        qDebug() << "   数据点数:" << avgMassX.size();
        qDebug() << "   X轴范围:" << (avgMassX.isEmpty() ? 0 : avgMassX.first())
                 << "~" << (avgMassX.isEmpty() ? 0 : avgMassX.last());

        return true;
    }
    catch (const std::exception &e)
    {
        qDebug() << "LxCustomDataReader::loadMassDataForAvg: 异常:" << e.what();
        return false;
    }
    catch (...)
    {
        qDebug() << "LxCustomDataReader::loadMassDataForAvg: 未知异常";
        return false;
    }
}

bool LxCustomDataReader::parseMassData(const QByteArray &dataY, const QByteArray &streamBody,
                                       QVector<double> &massX, QVector<double> &massY)
{
    if (dataY.isEmpty() || streamBody.isEmpty())
    {
        qDebug() << "LxCustomDataReader::parseMassData: 输入数据为空";
        return false;
    }

    try
    {
        // 解析StreamBody获取数据类型和参数
        const StreamBody *body = reinterpret_cast<const StreamBody *>(streamBody.constData());
        if (!body)
        {
            qDebug() << "LxCustomDataReader::parseMassData: StreamBody解析失败";
            return false;
        }

        // 根据数据类型解析Y轴数据
        int dataCount = dataY.size() / sizeof(double); // 假设是double类型
        if (dataCount <= 0)
        {
            qDebug() << "LxCustomDataReader::parseMassData: 数据点数量为0";
            return false;
        }

        // 解析Y轴数据
        massY.clear();
        massY.reserve(dataCount);

        const double *yData = reinterpret_cast<const double *>(dataY.constData());
        for (int i = 0; i < dataCount; ++i)
        {
            massY.append(yData[i]);
        }

        // 生成X轴数据（m/z值）
        // 从StreamBody中获取真实的m/z范围参数
        massX.clear();
        massX.reserve(dataCount);

        // 从StreamBody获取真实的m/z范围
        QPair<double, double> mzRange = parseMzRangeFromStreamBody(streamBody);
        double startMz = mzRange.first;
        double endMz = mzRange.second;

        // 如果无法获取有效范围，直接失败
        if (startMz <= 0 || endMz <= startMz)
        {
            qDebug() << "LxCustomDataReader::parseMassData: 无法获取有效的m/z范围";
            qDebug() << "   读取到的范围:" << startMz << "~" << endMz;
            qDebug() << "   无法生成X轴数据，解析失败";
            return false;
        }

        double stepMz = (endMz - startMz) / (dataCount - 1);

        for (int i = 0; i < dataCount; ++i)
        {
            massX.append(startMz + i * stepMz);
        }

        qDebug() << "LxCustomDataReader::parseMassData: 解析完成";
        qDebug() << "   数据点数:" << dataCount;
        qDebug() << "   m/z范围:" << massX.first() << "~" << massX.last();
        qDebug() << "   强度范围:" << *std::min_element(massY.begin(), massY.end())
                 << "~" << *std::max_element(massY.begin(), massY.end());

        return true;
    }
    catch (const std::exception &e)
    {
        qDebug() << "LxCustomDataReader::parseMassData: 异常:" << e.what();
        return false;
    }
    catch (...)
    {
        qDebug() << "LxCustomDataReader::parseMassData: 未知异常";
        return false;
    }
}

bool LxCustomDataReader::parseSegmentFromStreamHead(const QByteArray &streamHead, FileData *data)
{
    // qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: 开始解析Segment信息";
    // qDebug() << "   StreamHead大小:" << streamHead.size();

    if (streamHead.isEmpty())
    {
        qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: StreamHead为空";
        return false;
    }

    // 按照DataReader::splitStreamHead的逻辑解析StreamHead
    _StreamHead *pStreamHead = (_StreamHead *)streamHead.constData();

    // 验证StreamHead的有效性
    if (streamHead.size() < sizeof(_StreamHead))
    {
        qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: StreamHead大小不足";
        qDebug() << "   实际大小:" << streamHead.size() << ", 需要:" << sizeof(_StreamHead);
        return false;
    }

    // qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: StreamHead信息";
    // qDebug() << "   length:" << pStreamHead->length;
    // qDebug() << "   dateTime:" << pStreamHead->dateTime;

    // 解析StreamHead中的参数列表
    QList<cParamValue::_StreamHeadParam *> tmpList;
    if (!_StreamHead::toList(streamHead, tmpList))
    {
        qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: 解析StreamHead参数列表失败";
        return false;
    }

    // qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: 找到" << tmpList.size() << "个参数";

    // 查找Segment参数
    bool foundSegment = false;
    for (int i = 0; i < tmpList.size(); ++i)
    {
        if (tmpList[i]->type == cParamValue::Type_Segment_Param)
        {
            // qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: 找到Segment参数，索引:" << i;

            // 创建Segment的QByteArray
            QByteArray segment;
            segment.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(segment.data(), tmpList[i]->param, segment.size());

            // 添加到FileData的mSegment列表
            data->mSegment.append(segment);
            foundSegment = true;

            // qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: 成功添加Segment";
            // qDebug() << "   Segment大小:" << segment.size();

            // 验证Segment内容
            if (segment.size() >= sizeof(cParamValue::_Segment))
            {
                const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segment.constData());
                // qDebug() << "   Segment事件数量:" << pSegment->countsEvent;
                // qDebug() << "   Segment类型:" << pSegment->type;
            }

            break;
        }
    }

    if (!foundSegment)
    {
        qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: 未找到Segment参数";
        return false;
    }

    // qDebug() << "LxCustomDataReader::parseSegmentFromStreamHead: Segment解析完成";
    return true;
}

QPair<double, double> LxCustomDataReader::getMzRangeFromSegment(FileData *data, int eventId)
{
    qDebug() << "LxCustomDataReader::getMzRangeFromSegment: 开始从Segment获取m/z范围";
    qDebug() << "   事件ID:" << eventId;

    // 检查FileData是否有Segment信息
    if (data->mSegment.empty())
    {
        qDebug() << "LxCustomDataReader::getMzRangeFromSegment: 没有Segment信息";
        return qMakePair(0.0, 0.0);
    }

    // 解析第一个Segment的QByteArray数据
    const QByteArray &segmentData = data->mSegment[0];
    if (segmentData.size() < sizeof(cParamValue::_Segment))
    {
        qDebug() << "LxCustomDataReader::getMzRangeFromSegment: Segment数据不完整";
        qDebug() << "   数据大小:" << segmentData.size() << ", 需要:" << sizeof(cParamValue::_Segment);
        return qMakePair(0.0, 0.0);
    }

    // 将QByteArray转换为_Segment结构体
    const cParamValue::_Segment *pSegment = reinterpret_cast<const cParamValue::_Segment *>(segmentData.constData());

    if (pSegment->countsEvent == 0)
    {
        qDebug() << "LxCustomDataReader::getMzRangeFromSegment: Segment中没有事件";
        return qMakePair(0.0, 0.0);
    }

    // 获取第一个事件的指针
    cParamValue::_Event *pEvent = (cParamValue::_Event *)((char *)&(pSegment->fisrtEvent));

    qDebug() << "LxCustomDataReader::getMzRangeFromSegment: 事件类型:" << pEvent->type;

    if (pEvent->type == cParamValue::Type_Scan)
    {
        // Scan事件：使用线性扫描范围
        cParamValue::_EventScan *pEventScan = (cParamValue::_EventScan *)pEvent;
        double startMass = pEventScan->msStart; // 正确的成员名称
        double endMass = pEventScan->msEnd;     // 正确的成员名称

        if (startMass > 0 && endMass > startMass)
        {
            qDebug() << "LxCustomDataReader::getMzRangeFromSegment: Scan事件，m/z范围:" << startMass << "~" << endMass;
            return qMakePair(startMass, endMass);
        }
        else
        {
            qDebug() << "LxCustomDataReader::getMzRangeFromSegment: Scan事件范围无效";
            qDebug() << "   起始质量:" << startMass << ", 结束质量:" << endMass;
            return qMakePair(0.0, 0.0);
        }
    }
    else if (pEvent->type == cParamValue::Type_SIM || pEvent->type == cParamValue::Type_SIM_2048)
    {
        // SIM事件：使用mass数组中的真实质量值
        cParamValue::_EventSIM *pEventSIM = (cParamValue::_EventSIM *)pEvent;

        // 从mass数组中提取有效的质量值范围
        double minValidMass = 999999.0, maxValidMass = 0.0;
        int validCount = 0;

        for (int i = 0; i < 2048; ++i)
        {
            if (pEventSIM->mass[i] > 0.0001) // 有效的质量值
            {
                minValidMass = qMin(minValidMass, pEventSIM->mass[i]);
                maxValidMass = qMax(maxValidMass, pEventSIM->mass[i]);
                validCount++;
            }
        }

        if (validCount > 0 && maxValidMass > minValidMass)
        {
            qDebug() << "LxCustomDataReader::getMzRangeFromSegment: SIM事件，m/z范围:" << minValidMass << "~" << maxValidMass;
            qDebug() << "   有效质量值数量:" << validCount;
            return qMakePair(minValidMass, maxValidMass);
        }
        else
        {
            qDebug() << "LxCustomDataReader::getMzRangeFromSegment: SIM质量值无效";
            qDebug() << "   有效质量值数量:" << validCount;
            return qMakePair(0.0, 0.0);
        }
    }
    else
    {
        qDebug() << "LxCustomDataReader::getMzRangeFromSegment: 未知事件类型:" << pEvent->type;
        return qMakePair(0.0, 0.0);
    }
}
