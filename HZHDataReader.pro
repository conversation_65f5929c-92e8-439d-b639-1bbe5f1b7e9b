QT       += core gui charts concurrent xml

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++17
# 在.pro文件中添加以下内容
# QMAKE_CXXFLAGS += -Wno-unused-parameter -Wreturn-type  -Wno-*-clazy-range-loop-detach -Wunused-variable -Wsign-compare
# -Wno-unused-variable -Wno-reorder
QMAKE_CXXFLAGS += -Wno-unused-parameter -Wreturn-type -Wno-*-clazy-range-loop-detach -Wunused-variable -Wsign-compare -Wno-unused-variable -Wno-reorder


# The following define makes your compiler emit warnings if you use
# any Qt feature that has been marked deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS

# 解决Windows头文件中byte类型与std::byte冲突的问题
win32 {
    DEFINES += WIN32_LEAN_AND_MEAN
    DEFINES += NOMINMAX
    # 在包含Windows头文件之前重定义byte类型
    QMAKE_CXXFLAGS += -Dbyte=win_byte_override
}

# You can also make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

HEADERS += \
    mainwindow.h \
    nanoflann.hpp

SOURCES += \
    main.cpp \
    mainwindow.cpp
# UI文件
FORMS += \
    mainwindow.ui

# # 源文件
# SOURCES +=
#     main.cpp \
#     mainwindow.cpp
# # 头文件
# HEADERS +=
#     GlobalDefine.h \
#     mainwindow.h \
#     nanoflann.hpp
# # UI文件
# FORMS += \
#     mainwindow.ui



# 包含LxChart项目
include($$PWD/LxChart/LxChart.pri)
include($$PWD/FileData/FileData.pri)
include($$PWD/LxDataReader/LxDataReader.pri)
include($$PWD/Algorithm/Algorithm.pri)
include($$PWD/CustomControl/CustomControl.pri)
include($$PWD/Config/Config.pri)
include($$PWD/Globals/Globals.pri)

# PC = PC
# # 判断 PC 是否为 HUAWEI
# equals(PC, HUAWEI) {
#     Dir = D:/StudyAndWork/QtProjects
# } else {
#     Dir = Z:/StudyAndWork/QtProjects
# }
Dir = D:/StudyAndWork/QtProjects
# 添加原项目的库路径和库依赖
INCLUDEPATH += $$Dir/DataAnalysisHZH
INCLUDEPATH += D:\\QT\\GlobalStruct
# 移除DataFile库的头文件路径（已使用自定义读取器替代）
# INCLUDEPATH += $$Dir/DataAnalysisHZH/LibDataFileD/include
INCLUDEPATH += $$Dir/LxLibs/include

# 或者直接使用Eigen库的根目录
# INCLUDEPATH += D:\\StudyAndWork\\Eigen

# 设置库文件所在的目录
LIBS_DIR = $$Dir/LxLibs/libs


# 调试输出，验证配置
message("Final CONFIG: $$CONFIG")


# 当且仅当 CONFIG 精确为 debug 时
win32:CONFIG(debug, debug|release) {
    LIBS += -L$$LIBS_DIR -lDefineCommandsd
}
else:win32:CONFIG(release, debug|release) {
    LIBS += -L$$LIBS_DIR -lDefineCommands
}




# 移除 DataFile 库依赖（已使用自定义读取器替代）
# LIBS += -L$$Dir/DataAnalysisHZH/LibDataFileD/lib -lDataFile
LIBS += -L$$Dir/DataAnalysisHZH/LibDataFilterR/lib -lDataFilter
LIBS += -L$$Dir/DataAnalysisHZH/LibPeakAlgorithmR/lib -lpeakAlgorithm
# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

# DISTFILES += \
#     Algorithm/Algorithm.pri \
#     CustomControl/CustomControl.pri \
#     FileData/FileData.pri \
#     LxChart/LxChart.pri \
#     LxDataReader/LxDataReader.pri

RESOURCES += \
    res.qrc

# 复制必要的DLL文件到输出目录
win32 {
    CONFIG(debug, debug|release) {
        DESTDIR = debug
        # 移除DataFile.dll复制（已使用自定义读取器替代）
        # QMAKE_POST_LINK += $$quote(copy /Y \"$$Dir\\DataAnalysisHZH\\LibDataFileD\\lib\\DataFile.dll\" \"$$DESTDIR\\\" $$escape_expand(\\n\\t))
        # 复制dataFilter.dll
        QMAKE_POST_LINK += $$quote(copy /Y \"$$Dir\\DataAnalysisHZH\\LibDataFilterR\\lib\\dataFilter.dll\" \"$$DESTDIR\\\" $$escape_expand(\\n\\t))
        # 复制peakAlgorithm.dll
        QMAKE_POST_LINK += $$quote(copy /Y \"$$Dir\\DataAnalysisHZH\\LibPeakAlgorithmR\\lib\\peakAlgorithm.dll\" \"$$DESTDIR\\\" $$escape_expand(\\n\\t))
    } else {
        DESTDIR = release
        # 移除DataFile.dll复制（已使用自定义读取器替代）
        # QMAKE_POST_LINK += $$quote(copy /Y \"$$Dir\\DataAnalysisHZH\\LibDataFileD\\lib\\DataFile.dll\" \"$$DESTDIR\\\" $$escape_expand(\\n\\t))
        # 复制dataFilter.dll
        QMAKE_POST_LINK += $$quote(copy /Y \"$$Dir\\DataAnalysisHZH\\LibDataFilterR\\lib\\dataFilter.dll\" \"$$DESTDIR\\\" $$escape_expand(\\n\\t))
        # 复制peakAlgorithm.dll
        QMAKE_POST_LINK += $$quote(copy /Y \"$$Dir\\DataAnalysisHZH\\LibPeakAlgorithmR\\lib\\peakAlgorithm.dll\" \"$$DESTDIR\\\" $$escape_expand(\\n\\t))
    }
}


