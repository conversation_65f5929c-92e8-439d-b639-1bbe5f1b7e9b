#include <QCoreApplication>
#include <QDebug>
#include <QFileInfo>
#include "LxDataReader/lxcustomdatareader.h"
#include "FileData/filedata.h"

/**
 * @brief 测试MASS数据加载修复
 * 
 * 这个程序用于验证修复后的MASS数据加载是否正常工作
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== MASS数据加载修复测试 ===";
    
    // 测试文件路径
    QString testFilePath = "C:/Users/<USER>/Desktop/新版数据格式20250717/20250724-抗生素-C1.Param";
    
    // 检查文件是否存在
    QFileInfo fileInfo(testFilePath);
    if (!fileInfo.exists()) {
        qDebug() << "❌ 测试文件不存在:" << testFilePath;
        return -1;
    }
    
    qDebug() << "📁 测试文件:" << testFilePath;
    
    try {
        // 创建自定义数据读取器
        LxCustomDataReader *customReader = new LxCustomDataReader();
        
        // 创建FileData对象
        FileData *testData = new FileData(testFilePath);
        
        qDebug() << "\n=== 第1步：加载TIC数据 ===";
        
        // 加载TIC数据
        bool ticSuccess = customReader->loadTICDataComplete(testData);
        
        if (!ticSuccess) {
            qDebug() << "❌ TIC数据加载失败";
            delete testData;
            delete customReader;
            return -1;
        }
        
        qDebug() << "✅ TIC数据加载成功";
        
        // 检查关键数组
        qDebug() << "📊 timePoints数组大小:" << testData->timePoints.size();
        qDebug() << "📊 frameIndices数组大小:" << testData->frameIndices.size();
        qDebug() << "📊 indexArray数组大小:" << testData->getIndexArray().size();
        
        if (testData->timePoints.isEmpty() || testData->frameIndices.isEmpty()) {
            qDebug() << "❌ 关键数组为空，MASS加载将失败";
            delete testData;
            delete customReader;
            return -1;
        }
        
        qDebug() << "\n=== 第2步：测试MASS数据加载 ===";
        
        // 测试几个不同的时间点
        QVector<double> testTimePoints;
        if (testData->timePoints.size() > 0) {
            testTimePoints.append(testData->timePoints.first());  // 第一个点
        }
        if (testData->timePoints.size() > 10) {
            testTimePoints.append(testData->timePoints[10]);      // 第11个点
        }
        if (testData->timePoints.size() > 100) {
            testTimePoints.append(testData->timePoints[100]);     // 第101个点
        }
        
        for (double timePoint : testTimePoints) {
            qDebug() << "\n--- 测试时间点:" << timePoint << "秒 ---";
            
            // 查找对应的帧索引（模拟FileDataManager::loadMassDataForFrame的逻辑）
            int frameIndex = -1;
            for (int i = 0; i < testData->timePoints.size(); ++i) {
                if (qAbs(testData->timePoints[i] - timePoint) < 0.001) {  // 允许小误差
                    frameIndex = i;
                    break;
                }
            }
            
            if (frameIndex < 0) {
                qDebug() << "❌ 找不到对应的帧索引";
                continue;
            }
            
            qDebug() << "📍 帧索引:" << frameIndex;
            
            if (frameIndex >= testData->frameIndices.size()) {
                qDebug() << "❌ 帧索引超出frameIndices范围";
                continue;
            }
            
            qint64 frameIndexValue = testData->frameIndices[frameIndex];
            qDebug() << "📍 帧索引值:" << frameIndexValue;
            
            // 测试MASS数据加载
            const std::vector<qint64> &indexArray = testData->getIndexArray();
            QByteArray massData, streamBody;
            
            bool massSuccess = customReader->loadMassData(frameIndex, indexArray, massData, streamBody, testFilePath);
            
            if (massSuccess) {
                qDebug() << "✅ MASS数据加载成功";
                qDebug() << "📊 MASS数据大小:" << massData.size() << "字节";
                qDebug() << "📊 StreamBody大小:" << streamBody.size() << "字节";
                
                // 计算质谱数据点数（假设是double类型）
                if (massData.size() > 0) {
                    int massPointCount = massData.size() / sizeof(double);
                    qDebug() << "🔬 质谱数据点数:" << massPointCount;
                }
            } else {
                qDebug() << "❌ MASS数据加载失败";
            }
        }
        
        qDebug() << "\n=== 测试完成 ===";
        qDebug() << "如果看到多个'✅ MASS数据加载成功'，说明修复成功！";
        qDebug() << "如果仍然看到'❌ MASS数据加载失败'，可能需要进一步调试。";
        
        // 清理资源
        delete testData;
        delete customReader;
        
        return 0;
        
    } catch (const std::exception &e) {
        qDebug() << "❌ 程序异常:" << e.what();
        return -1;
    } catch (...) {
        qDebug() << "❌ 未知异常";
        return -1;
    }
}
