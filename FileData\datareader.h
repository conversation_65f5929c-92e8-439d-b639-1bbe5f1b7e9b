#ifndef DATAREADER_H
#define DATAREADER_H

#include <QObject>
#include <QVector>
#include <QMutex>
#include <QAtomicInt>
#include <vector>
#include "filedata.h"
// 按照示例项目的头文件引用方式
#include <cDataFileRead.h>
#include "LibDataFilterR/include/cDataFilter.h"
#include <sMethod/cConfigOMS.h>
#include "method/cParamCCS.h"
#include "LibPeakAlgorithmR/include/cPeakAlgorithm.h"
// 按照示例项目包含GlobalStruct头文件
#ifdef Q_OS_WIN32
#include "D:/QT/GlobalStruct/cPublicCCS.h"
#else
#include "/home/<USER>/work/GlobalStruct/cPublicCCS.h"
#endif
#include "taskmanager.h"
#include <QDateTime>
#include "Globals/GlobalDefine.h"
#include <QSettings>
#include <QCoreApplication>
#define xicThresholdValue 0.5 // xic范围阈值

// 前向声明
struct XicDataPoint;

// _PARAM_XIC结构体已经在库头文件中定义，不需要重复定义

class DataReader : public QObject
{
    Q_OBJECT

public:
    DataReader(QObject *parent = nullptr);
    ~DataReader();

    // 加载TIC数据
    bool loadTICData(FileData &data);

    // 加载质谱数据
    bool loadMassData(int eventId, qint64 frameIndex, FileData &data);
    // 读取平均质谱其中之一
    bool loadMassDataForAvg(int eventId, QVector<int> frameIndexVec, FileData &data);

    // 为了读取Xic数据而加载Mass数据
    bool loadMassDataForXic(qint64 frameIndex, FileData &data, double timePoint, double timePointIndex, double mz);
    // 加载所有的质谱数据
    bool loadXicData(FileData &data, int eventId, double mz);

    // 新增：批量加载XIC数据的优化版本
    bool loadXicDataBatch(FileData &data, int eventId, double mz);

    // 新增：提取单个m/z的强度值（不涉及Qt对象操作）
    bool extractIntensityForMz(qint64 frameIndex, FileData &data, double mz, double &extractedIntensity);

    // 新增：在工作线程中设置XIC数据
    void setXicDataInWorkerThread(FileData &data, int eventId, const QVector<struct XicDataPoint> &xicDataPoints, qint64 elapsedTime);

    // 新增：批量设置XIC数据
    void setXicDataBatch(FileData &data, int eventId, const QVector<struct XicDataPoint> &xicDataPoints, qint64 elapsedTime);

    // 获取对应的Dat文件路径
    QString getDatFilePath(const QString &paramFileName);
    QString getTypeName(cParamValue::Type_Event type);
    cParamValue::_Segment *getSegment(int index, FileData &data)
    {
        if ((index > -1) && (index < data.mSegment.size()))
            return reinterpret_cast<cParamValue::_Segment *>(data.mSegment[index].data());
        return nullptr;
    }

    // 严格按照示例项目的dataDisassembleFirst函数签名
    uint32_t dataDisassembleFirst(QByteArray &pByteArray,
                                  cParamValue::_Segment *pSegment,
                                  QList<std::vector<double>> &pListX,
                                  QList<std::vector<double>> &pListY,
                                  QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> &pSTRUCT_DATA,
                                  bool restart = false);

    /**
     * @brief 获取TIC2XIC数据 此函数需要根据扫描类型或MRM类型进行不同方式的提取
     * @param data
     * @param Struct_MRM_Vec 这个参数只有在MRM类型时才会用到
     * @return  QPair<bool, bool> 第一个bool表示是否成功读取类型，如果false则弹窗类型读取失败。第二个bool：true 是扫描类型 false 是MRM类型
     * 根据这个返回值选择弹窗类型
     */
    QPair<bool, bool> get_TIC2XIC_Data(FileData &data, QVector<GlobalDefine::StructMRM> &Struct_MRM_Vec);

    // 从MRM事件中提取数据
    template <typename EventMRM>
    void extractMRMData(const EventMRM pEventMRM, uint32_t currentEvt, QVector<GlobalDefine::StructMRM> &Struct_MRM_Vec);

    void TIC2XIC(FileData &data);

    // 最外层QMap:所有Tic文件的平均质谱Map
    // 第二个Map:某个质谱文件里的某个事件对应的平均质谱Map tuple:0:平均质谱X数据 1:平均质谱Y数据 2:是否初始化X轴数据 3:需要除的点数（需要平均的总质谱数量）
    static QMap<QString, QMap<int, std::tuple<QVector<double>, QVector<double>, bool, int>>> avgMassMap; // 平均质谱Map

    static GlobalEnums::AvgMassStatus getAvgMassStatus();
    static void setAvgMassStatus(GlobalEnums::AvgMassStatus newAvgMassStatus);

    static bool isRefExist; // 背景区域是否存在

private:
    QMutex mutex;
    int splitStreamHead(QByteArray &segment, QByteArray &pStreamHead, QString &pPropertyStr,
                        QByteArray &pTuneFile); //, cParamValue::_Segment*& pSegment
    // 数据转换函数
    void convertTICData(FileData &data);

    // 注释：已移除getPeriodFromConfig函数，现在使用全局配置对象
    // 平均质谱计算状态 默认停止计算 只有处于Ready状态下才可以使用平均质谱数据
    static QAtomicInt avgMassStatus; // 使用原子变量确保线程安全

    cDataFilter *mDataProcess = nullptr;

    // 按照示例项目添加的成员变量
    QList<std::vector<quint32>> mPointTimeSIM; // SIM模式下的点时间数据
    _CONGIG_OMS::_PARAM_FIT mCALIBRATE;        // 校准数据，与示例项目类型一致

    // 按照示例项目添加的线程安全互斥锁
    QMutex mGraphBuffMutexTIC;  // TIC数据访问互斥锁
    QMutex mGraphBuffMutexMass; // MASS数据访问互斥锁

signals:
};

#endif // DATAREADER_H
