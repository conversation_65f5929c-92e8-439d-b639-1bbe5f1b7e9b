@echo off
echo 正在复制必要的DLL文件...

REM 创建debug目录（如果不存在）
if not exist "debug" mkdir debug

REM 移除DataFile.dll复制（已使用自定义读取器替代）
REM copy /Y "..\DataAnalysisHZH\LibDataFileD\lib\DataFile.dll" "debug\"
REM if %errorlevel% neq 0 (
REM     echo 错误：无法复制DataFile.dll
REM     pause
REM     exit /b 1
REM )

REM 复制dataFilter.dll
copy /Y "..\DataAnalysisHZH\LibDataFilterR\lib\dataFilter.dll" "debug\"
if %errorlevel% neq 0 (
    echo 错误：无法复制dataFilter.dll
    pause
    exit /b 1
)

REM 复制peakAlgorithm.dll
copy /Y "..\DataAnalysisHZH\LibPeakAlgorithmR\lib\peakAlgorithm.dll" "debug\"
if %errorlevel% neq 0 (
    echo 错误：无法复制peakAlgorithm.dll
    pause
    exit /b 1
)

echo DLL文件复制完成！
echo.
echo 复制的文件：
echo - dataFilter.dll
echo - peakAlgorithm.dll
echo 注意：DataFile.dll已移除（使用自定义读取器替代）
echo.
pause
