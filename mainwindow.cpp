#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QFileDialog>
#include <QMessageBox>
#include <QFile>
#include <QTextStream>
#include <QDir>
#include <QDebug>
#include <QTimer>
#include <QCoreApplication>
#include <QEventLoop>
#include <algorithm>

MainWindow::MainWindow(QWidget *parent) : QMainWindow(parent), ui(new Ui::MainWindow)
{
    ui->setupUi(this);
    installEventFilter(this);
    init();
    //*测试代码，随时可删除 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////

    lxDataReader = new LxDataReader;

    //*测试代码，随时可删除 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////
    qDebug() << "主线程" << QThread::currentThread();
}

MainWindow::~MainWindow()
{
    qDebug() << "MainWindow::~MainWindow: 开始析构，当前线程:" << QThread::currentThread();

    // 断开所有信号连接，防止析构过程中的异步调用
    if (fileDataManager)
    {
        disconnect(fileDataManager, nullptr, this, nullptr);
        qDebug() << "MainWindow::~MainWindow: 断开FileDataManager信号连接";
    }

    // 处理所有待处理的事件
    QCoreApplication::processEvents();

    // 安全清理图表对象
    if (ticXicChart)
    {
        qDebug() << "MainWindow::~MainWindow: 清理ticXicChart";
        try
        {
            ticXicChart->clearAllTicXicData();
        }
        catch (...)
        {
            qDebug() << "MainWindow::~MainWindow: 清理ticXicChart时发生异常";
        }
        ticXicChart = nullptr;
    }

    if (massChart)
    {
        qDebug() << "MainWindow::~MainWindow: 清理massChart";
        try
        {
            massChart->ClearAllLxChartData();
        }
        catch (...)
        {
            qDebug() << "MainWindow::~MainWindow: 清理massChart时发生异常";
        }
        massChart = nullptr;
    }

    // 再次处理事件，确保清理完成
    QCoreApplication::processEvents();

    qDebug() << "MainWindow::~MainWindow: 删除UI";
    delete ui;
    qDebug() << "MainWindow::~MainWindow: 析构完成";
}

void MainWindow::setupCharts()
{
    // 设置质谱图表 - 使用LxChart
    massChart = new LxMassChart(GlobalEnums::TrackType::MS, this);
    // massChart->setMaxChartDataCount(1);
    ui->gridLayout_Mass->addWidget(massChart);

    // 设置TIC/XIC组合图表
    ticXicChart = new LxTicXicChart(this);
    ticXicChart->enablePointSelection(true);
    ticXicChart->setInteractionMode(GlobalEnums::InteractionMode::Mode_Select);

    ui->gridLayout_TICXIC->addWidget(ticXicChart);
}

void MainWindow::connectAll()
{
    // 注意：LxMassChart的connectAll()已经在构造函数中调用了，不需要重复调用

    // 旧的单个XIC信号连接已删除，现在使用sg_showMultipleXicChart处理所有XIC加载
    connect(this, &MainWindow::sg_windowStateChanged, massChart, &LxChart::refreashLabelWidgetPos);
    connect(this, &MainWindow::sg_windowStateChanged, ticXicChart, &LxChart::refreashLabelWidgetPos);

    // 连接MASS双击显示多个XIC的信号
    connect(massChart, &LxMassChart::sg_showMultipleXicChart, fileDataManager, &FileDataManager::showMultipleXicChart);

    // TIC2XIC点击信号`
    connect(massChart, &LxChart::sg_TIC2XIC_Clicked, fileDataManager, &FileDataManager::slot_sg_TIC2XIC_Clicked);

    // 连接新的TIC/XIC组合图表信号
    connect(ticXicChart, &LxChart::sg_showMassChart, fileDataManager, &FileDataManager::showMassChart);
    connect(ticXicChart, &LxChart::sg_TIC2XIC_Clicked, fileDataManager, &FileDataManager::slot_sg_TIC2XIC_Clicked);

    // 连接计算平均质谱信号（从原ticChart迁移）
    connect(ticXicChart, &LxChart::sg_calAvgMass, fileDataManager, &FileDataManager::getAvgMass);

    // 连接TIC/XIC组合图表的TIC删除信号（需要联动删除MASS）
    connect(ticXicChart, &LxTicXicChart::ticDataRemoved, [this](int eventId, const QString &paramPath)
            {
        qDebug() << "MainWindow: 接收到TIC删除信号，事件ID:" << eventId << "，路径:" << paramPath;

        FileData *data = fileDataManager->FileData_Map.value(paramPath);
        if (!data) {
            qDebug() << "MainWindow: 找不到FileData，路径:" << paramPath;
            return;
        }

        qDebug() << "MainWindow: 找到FileData，开始查找TIC数据";
        qDebug() << "MainWindow: TicMap大小:" << data->TicMap.size();

        // 打印所有TicMap的键
        for (auto it = data->TicMap.begin(); it != data->TicMap.end(); ++it) {
            qDebug() << "MainWindow: TicMap包含事件ID:" << it.key();
            if (it.value() && it.value()->hasMassData()) {
                qDebug() << "MainWindow: 事件ID" << it.key() << "包含MASS数据";
            }
        }

        // TIC删除时，联动删除该文件的所有MASS曲线
        int totalRemovedCount = 0;
        for (auto ticIt = data->TicMap.begin(); ticIt != data->TicMap.end(); ++ticIt) {
            TicChartData *ticData = ticIt.value();
            if (ticData && ticData->hasMassData()) {
                MassChartData *massData = ticData->getMassData();
                if (massData) {
                    qDebug() << "MainWindow: 删除MASS数据，UniqueID:" << massData->getUniqueID();
                    massChart->RemoveLxChartDataByUniqueID(massData->getUniqueID());
                    totalRemovedCount++;
                }
            }
        }

        // 注意：MASS数据对象由TicChartData析构时自动删除，无需手动清理

        if (totalRemovedCount > 0) {
            qDebug() << "MainWindow: 成功删除" << totalRemovedCount << "个MASS数据";
        } else {
            qDebug() << "MainWindow: 没有找到MASS数据需要删除";
        }

        // 注意：XIC数据已经在LxTicXicChart::RemoveLxChartDataByUniqueID中处理了，不需要重复删除
        qDebug() << "MainWindow: XIC数据已由TIC删除时统一处理，无需额外清理";

        // 注意：TIC数据已经在ticXicChart内部处理，不需要额外删除

        qDebug() << "MainWindow: TIC删除联动处理完成"; });

    // 连接TIC/XIC组合图表的XIC删除信号（不影响TIC和MASS）
    connect(ticXicChart, &LxTicXicChart::xicDataRemoved, [this](int eventId, const QString &paramPath)
            {
                qDebug() << "XIC删除：事件ID" << eventId << "，不影响TIC和MASS";
                // XIC删除不需要额外处理，因为不影响其他数据
            });

    // 连接loadTIC信号
    connect(this, &MainWindow::sg_loadTIC, this, [this](const QString &fileName)
            {
        if (fileDataManager->FileData_Map.contains(fileName)) {
            qDebug() << "不能重复导入";
            return;
        }

        fileDataManager->generateFileData(fileName); });

    // 连接数据处理完成信号
    connect(fileDataManager, &FileDataManager::sg_sendTicChartData, this, [=](FileData *data)
            {
        // 添加TIC数据到TIC/XIC组合图表
        for (auto it = data->TicMap.begin(); it != data->TicMap.end(); it++) {
            // qDebug() << "添加TIC到ticXicChart:" << it.key();
            ticXicChart->addTicData(static_cast<TicChartData *>(it.value()));
        } });

    connect(fileDataManager, &FileDataManager::sg_sendXicChartData, this, [this](FileData *data)
            {
        try {
            qDebug() << "MainWindow: 接收到XIC数据信号，当前线程:" << QThread::currentThread();

            // 检查数据有效性
            if (!data) {
                qDebug() << "MainWindow: FileData指针为空，跳过添加";
                return;
            }

            // 查找包含XIC数据的TIC，获取最新创建的XIC
            XicChartData *xicData = nullptr;
            int xicEventId = -1;

            for (auto ticIt = data->TicMap.begin(); ticIt != data->TicMap.end(); ++ticIt) {
                TicChartData *ticData = ticIt.value();
                if (ticData && ticData->hasXicData()) {
                    // 获取最新的XIC数据
                    xicData = ticData->getLatestXicData();
                    if (xicData) {
                        xicEventId = ticIt.key();
                        qDebug() << "MainWindow: 找到XIC数据，总数:" << ticData->getXicDataList().size() << "，选择最新的XIC";
                        break;
                    }
                }
            }

            if (!xicData) {
                qDebug() << "MainWindow: 没有找到XIC数据，跳过添加";
                return;
            }

            qDebug() << "MainWindow: 准备添加XIC数据到ticXicChart，事件ID:" << xicEventId << "，路径:" << data->getFilePath()
                     << "，数据点数:" << xicData->getData().size();

            // 检查ticXicChart是否有效
            if (!ticXicChart) {
                qDebug() << "MainWindow: ticXicChart指针为空，无法添加XIC数据";
                return;
            }

            // 显示加载状态
            ui->statusbar->showMessage(tr("正在添加XIC数据到图表..."), 1000);

            // 异步添加XIC数据到TIC/XIC组合图表，避免阻塞UI
            QTimer::singleShot(0, this, [this, xicData, xicEventId]() {
                try {
                    qDebug() << "MainWindow: 开始异步调用ticXicChart->addXicData";
                    if (ticXicChart) {
                        ticXicChart->addXicData(xicData);
                        qDebug() << "MainWindow: XIC数据添加成功，事件ID:" << xicEventId;
                        ui->statusbar->showMessage(tr("XIC数据添加完成"), 2000);
                    }
                } catch (const std::exception &e) {
                    qDebug() << "MainWindow: XIC数据添加失败，异常:" << e.what();
                    ui->statusbar->showMessage(tr("XIC数据添加失败"), 3000);
                } catch (...) {
                    qDebug() << "MainWindow: XIC数据添加失败，未知异常";
                    ui->statusbar->showMessage(tr("XIC数据添加失败"), 3000);
                }
            });

            qDebug() << "MainWindow: XIC数据处理lambda即将结束";

            // 添加调试信息，检查对象状态
            qDebug() << "MainWindow: 检查对象状态 - data指针:" << (data ? "有效" : "无效") << "，xicData指针:" << (xicData ? "有效" : "无效")
                     << "，ticXicChart指针:" << (ticXicChart ? "有效" : "无效");

            qDebug() << "MainWindow: lambda完全结束";

            // 优化：使用更短的延迟和异步处理，减少UI阻塞
            QTimer::singleShot(10, this, [this]() {
                qDebug() << "MainWindow: XIC处理异步回调开始";

                // 轻量级事件处理，避免阻塞用户输入
                QCoreApplication::processEvents(QEventLoop::ExcludeUserInputEvents | QEventLoop::ExcludeSocketNotifiers, 5);

                // 再次检查对象状态
                if (ticXicChart) {
                    qDebug() << "MainWindow: 异步回调中ticXicChart状态正常";

                    // 异步更新图表显示
                    QTimer::singleShot(0, this, [this]() {
                        if (ticXicChart) {
                            ticXicChart->chart()->update();
                            ticXicChart->update();
                        }
                    });
                } else {
                    qDebug() << "MainWindow: 异步回调中ticXicChart已无效";
                }

                qDebug() << "MainWindow: XIC处理异步回调完成";
            });

        } catch (const std::exception &e) {
            qDebug() << "MainWindow: XIC lambda外层异常:" << e.what();
        } catch (...) {
            qDebug() << "MainWindow: XIC lambda外层未知异常";
        } });

    // 连接独立的XIC数据信号（使用Qt::QueuedConnection确保在主线程中执行）
    connect(
        fileDataManager, &FileDataManager::sg_sendIndependentXicChartData, this,
        [this](XicChartData *xicData)
        {
            qDebug() << "MainWindow: 接收到独立XIC数据信号，当前线程:" << QThread::currentThread();

            // 检查数据有效性
            if (!xicData)
            {
                qDebug() << "MainWindow: XicChartData指针为空，跳过添加";
                return;
            }

            // 添加线程安全检查
            if (QThread::currentThread() != this->thread())
            {
                qDebug() << "MainWindow: 警告：不在主线程中执行";
                return;
            }

            qDebug() << "MainWindow: 准备添加独立XIC数据到ticXicChart，事件ID:" << xicData->getTic_event_id() << "，UniqueID:" << xicData->getUniqueID()
                     << "，数据点数:" << xicData->getData().size();

            // 检查ticXicChart是否有效
            if (!ticXicChart)
            {
                qDebug() << "MainWindow: ticXicChart指针为空，无法添加XIC数据";
                return;
            }

            // 添加XIC数据到TIC/XIC组合图表
            try
            {
                qDebug() << "MainWindow: 开始调用ticXicChart->addXicData";
                ticXicChart->addXicData(xicData);
                qDebug() << "MainWindow: 独立XIC数据添加成功，事件ID:" << xicData->getTic_event_id();
            }
            catch (const std::exception &e)
            {
                qDebug() << "MainWindow: 独立XIC数据添加失败，异常:" << e.what();
            }
            catch (...)
            {
                qDebug() << "MainWindow: 独立XIC数据添加失败，未知异常";
            }

            // 确保所有Qt操作完成
            QCoreApplication::processEvents();

            qDebug() << "MainWindow: 独立XIC数据处理完成";

            // 添加额外的安全检查
            if (ticXicChart)
            {
                qDebug() << "MainWindow: ticXicChart仍然有效";
            }
            else
            {
                qDebug() << "MainWindow: 警告：ticXicChart已无效";
            }

            qDebug() << "MainWindow: lambda即将结束";
        },
        Qt::QueuedConnection);

    connect(fileDataManager, &FileDataManager::sg_sendMassChartData, this, [this](int eventId, qint64 frameIndex, FileData *data)
            {
        // TODO: 重构MASS数据处理逻辑，使用新的TicChartData结构
        // 暂时注释掉旧的MASS_Map逻辑
        // qDebug() << "接收MASS数据，事件ID:" << eventId << "，帧索引:" << frameIndex;

        // 从TicMap中获取对应的TIC数据
        TicChartData *ticData = data->getTicData(eventId);
        if (!ticData || !ticData->hasMassData()) {
            qDebug() << "MainWindow: 找不到对应的TIC数据或MASS数据";
            LxTicXicChart::clearMassLoadingFlag(); // 清除加载标志
            return;
        }

        MassChartData *massData = ticData->getMassData();
        if (!massData) {
            qDebug() << "MainWindow: MASS数据为空";
            LxTicXicChart::clearMassLoadingFlag(); // 清除加载标志
            return;
        }

        // qDebug() << "添加MASS数据到图表";

        // 首先清除所有现有的峰标记和峰数据
        massChart->clearAllPeakGraphicsItems();

        // 重置所有现有曲线的寻峰标志和峰数据
        for (LxChartData *existingData : massChart->m_chartDataVec) {
            if (existingData) {
                existingData->setHasFindPeak(false);
                existingData->peakVec.clear();
                // qDebug() << "MainWindow: 重置现有曲线寻峰状态，UniqueID:" << existingData->getUniqueID();
            }
        }

        // 添加MASS曲线到图表
        massData->setHasFindPeak(false);
        massData->peakVec.clear();

        // qDebug() << "添加MASS曲线，ID:" << massData->getUniqueID();
        massChart->AddLxChartData(massData);
        // qDebug() << "MASS曲线添加完成，图表曲线数:" << massChart->m_chartDataVec.size();

        // qDebug() << "开始寻峰，方法索引:" << ui->comboBox_findPeaks->currentIndex();

        // 获取当前寻峰配置
        PeakFindingConfig config = PeakFindingConfigManager::instance().getConfig();
        qDebug() << "MainWindow获取到的配置 - 使用默认:" << config.useDefault;
        qDebug() << "MainWindow获取到的配置 - 使用默认:" << config.useDefault;

        // 检查是否需要强制重新寻峰（自定义参数时总是重新寻峰）
        bool forceReprocess = !config.useDefault;

        if (config.useDefault) {
            // 使用默认参数
            PeakFind::searchPeaksWithDefaultParams(massChart);

        } else {
            // 使用自定义配置参数
            qDebug() << "使用自定义寻峰配置参数";
            PeakFind::searchPeaksWithConfig(massChart, config, forceReprocess);
        }

        // 清除MASS加载状态标志
        LxTicXicChart::clearMassLoadingFlag(); });

    // 连接读取文件完成信号
    connect(fileDataManager, &FileDataManager::sg_readFileFinished, this, [=](bool success, const QString &filePath)
            {
        if (success) {
            ui->statusbar->showMessage(tr("文件读取成功: %1").arg(filePath), 3000);
        } else {
            ui->statusbar->showMessage(tr("文件读取失败: %1").arg(filePath), 3000);
        } });

    // 连接质谱帧加载完成信号
    connect(fileDataManager, &FileDataManager::sg_massFrameReady, this, [=](bool success, const QString &filePath, int frameIndex)
            {
        if (success) {
            ui->statusbar->showMessage(tr("帧 %1 加载成功: %2").arg(frameIndex).arg(filePath), 3000);
        } else {
            ui->statusbar->showMessage(tr("帧 %1 加载失败: %2").arg(frameIndex).arg(filePath), 3000);
        } });

    connect(ui->btn_options, &QPushButton::clicked, optionDialog, &OptionsDialog::show);

    // 连接图表的removeData信号到FileDataManager
    connect(ticXicChart, &LxChart::sg_removeData, fileDataManager, &FileDataManager::removeData);
    connect(massChart, &LxChart::sg_removeData, fileDataManager, &FileDataManager::removeData);

    // 连接批量模式信号
    connect(fileDataManager, &FileDataManager::sg_beginBatchAdd, ticXicChart, &LxTicXicChart::beginBatchAdd);
    connect(fileDataManager, &FileDataManager::sg_endBatchAdd, ticXicChart, &LxTicXicChart::endBatchAdd);

    qDebug() << "MainWindow: 所有信号槽连接完成";
}

void MainWindow::setupUI()
{
    setupCharts();

    // 设置包含gridLayout_top的Widget的objectName
    // 方法1：通过Layout找到父Widget
    if (ui->gridLayout_top)
    {
        QWidget *parentWidget = ui->gridLayout_top->parentWidget();
        if (parentWidget)
        {
            parentWidget->setObjectName("gridLayout_top");
            qDebug() << "设置父Widget的objectName为gridLayout_top，Widget类型:" << parentWidget->metaObject()->className();
        }
    }

    setWindowTitle("质谱数据分析工具");
    resize(1000, 800);

    // 设置状态栏初始信息
    ui->statusbar->showMessage("就绪");

    //*初始化UI控件 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
    optionDialog = new OptionsDialog(this);
    //*初始化UI控件 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////
}

void MainWindow::on_btn_add_clicked()
{
    QString fileName =
        QFileDialog::getOpenFileName(this, "打开文件", QStandardPaths::writableLocation(QStandardPaths::DesktopLocation), "TIC文件 (*.Param);;临时文件 (*.P)");

    if (fileName.isEmpty())
    {
        return;
    }

    emit sg_loadTIC(fileName);
}

void MainWindow::init()
{
    // 创建FileDataManager实例
    fileDataManager = new FileDataManager(this);

    // 设置线程池最大线程数（使用系统核心数的一半，至少2个线程）
    int threadCount = qMax(2, QThread::idealThreadCount() / 2);
    // 直接使用TaskManager单例设置线程池大小
    TaskManager::instance()->setMaxThreadCount(threadCount);

    qDebug() << "主线程：" << QThread::currentThread();
    qDebug() << "线程池初始化完成，最大线程数：" << TaskManager::instance()->maxThreadCount();
    qDebug() << "---wr------------------";

    // 移除了PeakFindingConfig.xml相关的初始化，现在只使用OptionsDialog.xml

    // 初始化OptionsDialog配置系统
    initOptionsDialogConfig();

    // 加载MainWindow专用样式
    loadMainWindowStyles();

    setupUI();
    connectAll();
}

void MainWindow::loadMainWindowStyles()
{
    ui->gridLayout_top->setObjectName("gridLayout_top");
    // 加载MainWindow专用的QSS文件
    QString stylePath = ":/QssFiles/MainWindowStyle.qss";

    QFile file(stylePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream stream(&file);
        QString styleSheet = stream.readAll();
        file.close();

        // 应用样式到MainWindow
        this->setStyleSheet(styleSheet);
        // qDebug() << "MainWindow样式加载成功，路径:" << stylePath;
        // qDebug() << "样式内容长度:" << styleSheet.length() << "字符";
        // qDebug() << "样式内容前100字符:" << styleSheet.left(100);
    }
    else
    {
        qWarning() << "MainWindow样式文件加载失败，路径:" << stylePath;
    }
}

// ==================== 重新积分功能实现 ====================

void MainWindow::reintegrateAllCharts()
{
    qDebug() << "MainWindow::reintegrateAllCharts: 开始重新积分所有图表";

    // 获取最新的寻峰配置
    PeakFindingConfig config = PeakFindingConfigManager::instance().getConfig();
    qDebug() << "MainWindow::reintegrateAllCharts: 使用配置 - 默认参数:" << config.useDefault;

    int totalCurves = 0;
    int processedCurves = 0;

    // 1. 重新积分TIC/XIC图表中的所有曲线
    if (ticXicChart && !ticXicChart->m_chartDataVec.isEmpty())
    {
        qDebug() << "MainWindow::reintegrateAllCharts: 开始处理TIC/XIC图表，曲线数:" << ticXicChart->m_chartDataVec.size();

        // 清除所有峰标记和积分区域
        ticXicChart->clearAllPeakGraphicsItems();

        // 重置所有曲线的寻峰状态
        for (LxChartData *data : ticXicChart->m_chartDataVec)
        {
            if (data)
            {
                data->setHasFindPeak(false);
                data->peakVec.clear();
                totalCurves++;
                qDebug() << "MainWindow::reintegrateAllCharts: 重置TIC/XIC曲线寻峰状态，UniqueID:" << data->getUniqueID();
            }
        }

        // 使用配置参数进行寻峰
        if (config.useDefault)
        {
            qDebug() << "MainWindow::reintegrateAllCharts: TIC/XIC使用默认参数寻峰";
            PeakFind::searchPeaksWithDefaultParams(ticXicChart);
        }
        else
        {
            qDebug() << "MainWindow::reintegrateAllCharts: TIC/XIC使用自定义配置参数寻峰";
            PeakFind::searchPeaksWithConfig(ticXicChart, config, true); // 强制重新处理
        }

        processedCurves += ticXicChart->m_chartDataVec.size();
    }

    // 2. 重新积分MASS图表中的所有曲线
    if (massChart && !massChart->m_chartDataVec.isEmpty())
    {
        qDebug() << "MainWindow::reintegrateAllCharts: 开始处理MASS图表，曲线数:" << massChart->m_chartDataVec.size();

        // 清除所有峰标记和积分区域
        massChart->clearAllPeakGraphicsItems();

        // 重置所有曲线的寻峰状态
        for (LxChartData *data : massChart->m_chartDataVec)
        {
            if (data)
            {
                data->setHasFindPeak(false);
                data->peakVec.clear();
                totalCurves++;
                qDebug() << "MainWindow::reintegrateAllCharts: 重置MASS曲线寻峰状态，UniqueID:" << data->getUniqueID();
            }
        }

        // 使用配置参数进行寻峰
        if (config.useDefault)
        {
            qDebug() << "MainWindow::reintegrateAllCharts: MASS使用默认参数寻峰";
            PeakFind::searchPeaksWithDefaultParams(massChart);
        }
        else
        {
            qDebug() << "MainWindow::reintegrateAllCharts: MASS使用自定义配置参数寻峰";
            PeakFind::searchPeaksWithConfig(massChart, config, true); // 强制重新处理
        }

        processedCurves += massChart->m_chartDataVec.size();
    }

    qDebug() << "MainWindow::reintegrateAllCharts: 重新积分完成";
    qDebug() << "  总曲线数:" << totalCurves;
    qDebug() << "  处理曲线数:" << processedCurves;
    qDebug() << "  使用默认参数:" << config.useDefault;

    // 更新状态栏
    ui->statusbar->showMessage(tr("重新积分完成：处理了 %1 条曲线").arg(processedCurves), 5000);
}

// ==================== OptionsDialog配置初始化 ====================

void MainWindow::initOptionsDialogConfig()
{
    qDebug() << "MainWindow::initOptionsDialogConfig: 开始初始化OptionsDialog配置系统";

    // 设置OptionsDialog配置文件路径
    QString optionsDialogConfigPath = GlobalDefine::GlobalPaths::getConfigDir() + "/OptionsDialog.xml";
    qDebug() << "MainWindow::initOptionsDialogConfig: 配置文件路径：" << optionsDialogConfigPath;

    // 设置配置文件路径
    OptionsDialogSettings::getInstance().setConfigFilePath(optionsDialogConfigPath);

    // 检查文件是否存在
    QFile configFile(optionsDialogConfigPath);
    if (!configFile.exists())
    {
        qDebug() << "MainWindow::initOptionsDialogConfig: OptionsDialog.xml不存在，将创建默认配置文件";
        qDebug() << "MainWindow::initOptionsDialogConfig: 使用原始UI参数作为默认值";
    }
    else
    {
        qDebug() << "MainWindow::initOptionsDialogConfig: OptionsDialog.xml存在，将读取现有配置";
    }

    // 加载配置（如果文件不存在，loadConfig会自动创建默认配置）
    bool loadSuccess = OptionsDialogSettings::getInstance().loadConfig();

    if (loadSuccess)
    {
        qDebug() << "MainWindow::initOptionsDialogConfig: 配置加载成功";

        // 检查是否是首次初始化
        if (OptionsDialogSettings::isFirstInit)
        {
            qDebug() << "MainWindow::initOptionsDialogConfig: 首次初始化，已使用原始UI参数创建默认配置";
        }
        else
        {
            qDebug() << "MainWindow::initOptionsDialogConfig: 从现有XML文件读取配置参数";
        }
    }
    else
    {
        qDebug() << "MainWindow::initOptionsDialogConfig: 配置加载失败，将使用程序内置默认值";
    }

    // 输出当前配置状态
    const PeakFindingParameters &peakParams = OptionsDialogSettings::getPeakFindingParametersSettings();
    const AppearanceSettings &appearanceSettings = OptionsDialogSettings::getAppearanceSettings();

    qDebug() << "MainWindow::initOptionsDialogConfig: 当前寻峰参数配置：";
    qDebug() << "  使用默认参数：" << peakParams.useDefault;
    qDebug() << "  噪声窗口：" << peakParams.noiseWindow;
    qDebug() << "  SNR噪声窗口倍数：" << peakParams.folderWidthOfNoise;

    qDebug() << "MainWindow::initOptionsDialogConfig: 当前外观配置：";
    qDebug() << "  图层标题字体：" << appearanceSettings.axisLabelFontFamily << appearanceSettings.axisLabelFontSize;
    qDebug() << "  手动标题字体：" << appearanceSettings.titleFontFamily << appearanceSettings.titleFontSize;
    qDebug() << "  谱图线宽：" << appearanceSettings.spectrumLineWidth;

    qDebug() << "MainWindow::initOptionsDialogConfig: OptionsDialog配置系统初始化完成";
}

bool MainWindow::eventFilter(QObject *obj, QEvent *event)
{
    if (event->type() == QEvent::WindowStateChange)
    {
        emit sg_windowStateChanged();
        qDebug() << "双击大小改变";
        return true;
    }

    // 其他事件放行，让后面的 filter 或目标对象自己接收
    return false;
}
