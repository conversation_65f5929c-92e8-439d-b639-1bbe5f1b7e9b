#include "mainwindow.h"

#include <QApplication>
#include <QCommandLineParser>
#include <QDebug>
#include "Globals/GlobalDefine.h"

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    // qDebug() << "应用程序启动，主线程:" << QThread::currentThread();

    // 初始化全局分析配置（模拟示例项目的配置加载）
    GlobalDefine::GlobalAnalysisConfig::getInstance();
    // qDebug() << "全局分析配置初始化完成";

    // 正常启动应用程序
    MainWindow w;
    w.show();

    int result = a.exec();
    qDebug() << "应用程序退出，返回值:" << result;

    return result;
}
