#include "datareader.h"
#include <QDebug>
#include <cmath>
#include <QElapsedTimer>
#include <QSemaphore>
#include <QAtomicInt>
#include <QCoreApplication>
#include <QMetaObject>
#include <QThread>

QAtomicInt DataReader::avgMassStatus(static_cast<int>(GlobalEnums::AvgMassStatus::Stop));
bool DataReader::isRefExist = false;
QMap<QString, QMap<int, std::tuple<QVector<double>, QVector<double>, bool, int>>> DataReader::avgMassMap;
DataReader::DataReader(QObject *parent) : QObject(parent)
{
    mDataProcess = sDataFilter1::getDataProcess();
}

DataReader::~DataReader()
{
}

// 注释：已移除getPeriodFromConfig函数，现在使用全局配置对象GlobalDefine::GlobalAnalysisConfig

// 从MRM事件中提取数据
template <typename EventMRM>
void DataReader::extractMRMData(const EventMRM pEventMRM, uint32_t currentEvt, QVector<GlobalDefine::StructMRM> &Struct_MRM_Vec)
{
    // 检查MRM数据是否有效
    if (pEventMRM->mass[0] < 0.0000001)
        return;

    // 遍历所有MRM通道
    for (int i = 0; i < pEventMRM->length(); ++i)
    {
        // 如果质量值为0，说明到达通道末尾
        // if (pEventMRM->mass[i] < 0.0000001)
        //     break;
        qDebug() << "title:" << QString(pEventMRM->title);
        double Q1 = pEventMRM->massPre[i]; // 前置四极杆质量值
        double Q3 = pEventMRM->mass[i];    // 后置四极杆质量值

        if (!(Q3 > 0 && Q1 > 0))
        {
            continue;
        }
        // 创建新的MRM结构体
        GlobalDefine::StructMRM mrm;
        mrm.Experiment = currentEvt + 1;        // Experiment从1开始
        mrm.ID = QString("G%1_q").arg(i + 1);   // 生成ID
        mrm.Q1 = Q1;                            // 前置四极杆质量值
        mrm.Q3 = Q3;                            // 后置四极杆质量值
        mrm.RT = pEventMRM->timeMs[i] / 1000.0; // 保留时间（转换为秒）
        mrm.massPre = pEventMRM->massPre[i];
        mrm.massPreOrig = pEventMRM->massPreOrig[i];
        mrm.timeMs = pEventMRM->timeMs[i];

        mrm.mass = pEventMRM->mass[i];
        mrm.massOrig = pEventMRM->massOrig[i];

        /*

        double mass[CHANNEL_COUNTS_MAX_2048];
        double massOrig[CHANNEL_COUNTS_MAX_2048];
        double timeMs[CHANNEL_COUNTS_MAX_2048]; // ms
        int length() const { return CHANNEL_COUNTS_MAX_2048; }
        int size() const { return sizeof(this); }

*/
        // 添加到向量中
        Struct_MRM_Vec.append(mrm);
    }
}

// 检测是否为MRM数据并获取MRM列表
QPair<bool, bool> DataReader::get_TIC2XIC_Data(FileData &data, QVector<GlobalDefine::StructMRM> &Struct_MRM_Vec)
{
    QMutexLocker locker(&mutex); // 使用RAII方式锁定互斥量

    // 清空之前的数据
    Struct_MRM_Vec.clear();

    // 获取当前段数据
    int sizeList = data.mSegment.size();
    if (sizeList < 1)
        return QPair(false, false);

    // 这里需要考虑每个段里的类型是否一致，现在暂不考虑，因为目前都是一个段。
    // 下面 tempP->countsEvent也是一样，本来也需要考虑 tempP->countsEvent>1的情况，但目前都是1所以也不考虑
    // 遍历所有段
    for (int index = 0; index < sizeList; ++index)
    {
        // 获取段数据
        cParamValue::_Segment *tempP = getSegment(index, data);
        qDebug() << "tempP->type：" << tempP->type;

        // 确认是否为三重四极杆数据
        if (tempP->type != cParamValue::Type_Seg_TripleQ)
            continue;
        // 遍历段中的所有事件
        int offsetP = 0;
        for (uint32_t currentEvt = 0; currentEvt < tempP->countsEvent; ++currentEvt)
        {
            // 获取事件数据
            cParamValue::_Event *pEvent = (cParamValue::_Event *)((char *)&(tempP->fisrtEvent) + offsetP);
            if (!pEvent)
                return QPair(false, false);

            QString typeStr;
            switch (pEvent->type)
            {
            case cParamValue::Type_MRM:
                typeStr = "MRM类型";
                break;
            case cParamValue::Type_MRM_2048:
                typeStr = "MRM_2048类型";

                break;
            case cParamValue::Type_Scan:

                typeStr = "Scan类型";
                break;
            case cParamValue::Type_Scan_RCT:
                typeStr = "Scan_RCT类型";

                break;
            case cParamValue::Type_Scan_RGA:
                typeStr = "Scan_RGA类型";

                break;
            default:
                typeStr = QString("错误类型%1").arg(pEvent->type);
                break;
            }
            qDebug() << "pEvent->type:" << typeStr;
            // 检查是否为MRM类型
            if (pEvent->type == cParamValue::Type_MRM || pEvent->type == cParamValue::Type_MRM_2048)
            {
                // 根据MRM类型进行处理
                if (pEvent->type == cParamValue::Type_MRM)
                {
                    cParamValue::_EventMRM *pEventMRM = (cParamValue::_EventMRM *)pEvent;
                    // 提取MRM数据
                    extractMRMData(pEventMRM, currentEvt, Struct_MRM_Vec);
                    qDebug() << "提取MRM";
                    // 更新偏移量
                    offsetP += sizeof(cParamValue::_EventMRM);
                }
                else
                {
                    cParamValue::_EventMRM2048 *pEventMRM = (cParamValue::_EventMRM2048 *)pEvent;
                    // 提取MRM数据
                    extractMRMData(pEventMRM, currentEvt, Struct_MRM_Vec);
                    qDebug() << "提取MRM2048";
                    // 更新偏移量
                    offsetP += sizeof(cParamValue::_EventMRM2048);
                }

                // 找到MRM数据，返回成功
                qDebug() << "MRM数据检测和提取成功，共找到" << Struct_MRM_Vec.size() << "个MRM数据点";
                return QPair(true, false);
            }

            else if (pEvent->type == cParamValue::Type_Scan || pEvent->type == cParamValue::Type_Scan_RCT || pEvent->type == cParamValue::Type_Scan_RGA)
            {
                // 如果是扫描类型则直接返回true
                return QPair(true, true);
            }
            else
            {
                qDebug() << "TIC2XIC类型不匹配，当前类型:" << pEvent->type;
                return QPair(false, false);
            }

            // else {
            //     // 其他类型，更新偏移量继续查找
            //     if (pEvent->type == cParamValue::Type_SIM) {
            //         offsetP += sizeof(cParamValue::_EventSIM);
            //     } else if (pEvent->type == cParamValue::Type_SIM_2048) {
            //         offsetP += sizeof(cParamValue::_EventSIM2048);
            //     } else if (pEvent->type == cParamValue::Type_Scan) {
            //         offsetP += sizeof(cParamValue::_EventScan);
            //     } else {
            //         // 未知类型，跳过
            //         offsetP += sizeof(cParamValue::_Event);
            //     }
            // }
        }
    }

    qDebug() << "未找到MRM数据";
    // 未找到MRM数据
    return QPair(false, false);
}

void DataReader::TIC2XIC(FileData &data)
{
    qDebug() << __FUNCTION__ << QThread::currentThread();
    // 使用示例
    QVector<GlobalDefine::StructMRM> mrmList;
    QPair<bool, bool> pair = get_TIC2XIC_Data(data, mrmList);
    if (pair.first && !pair.second)
    {
        // MRM数据检测成功
        qDebug() << "检测到" << mrmList.size() << "个MRM数据点";

        // 打印MRM列表
        for (int i = 0; i < mrmList.size(); ++i)
        {
            const GlobalDefine::StructMRM &mrm = mrmList[i];
            qDebug() << "MRM #" << (i + 1);
            qDebug() << "  Experiment:" << mrm.Experiment;
            qDebug() << "  ID:" << mrm.ID;
            qDebug() << "  Q1:" << mrm.Q1;
            qDebug() << "  Q3:" << mrm.Q3;
            qDebug() << "  RT:" << mrm.RT;
            qDebug() << "  massPre:" << mrm.massPre;
            qDebug() << "  massPreOrig:" << mrm.massPreOrig;
            qDebug() << "  timeMs:" << mrm.timeMs;
            qDebug() << "  mass:" << mrm.mass;
            qDebug() << "  massOrig:" << mrm.massOrig;
        }
    }
    else if (pair.first && pair.second)
    {
        // 检测到扫描类型
        qDebug() << "检测到扫描类型";
    }
    else if (!pair.first)
    {
        qDebug() << "TIC2XIC提取失败";
    }
}

bool DataReader::loadTICData(FileData &data)
{
    mutex.lock();

    // 严格按照示例项目的方式清空数据
    data.indexArray.clear();
    data.dataTIC_X.clear();
    data.dataTIC_Y.clear();
    data.otherLinesY.clear();
    data.pageTIC.clear();

    bool success = false;

    try
    {
        // 完全按照示例项目的方式：使用全局配置对象
        // 模拟示例项目的 getConfig()->Period 调用方式
        GlobalDefine::AnalysisConfig *pCONGIG_ANALYSIS = GlobalDefine::GlobalAnalysisConfig::getConfig();
        double period = pCONGIG_ANALYSIS->Period;

        qDebug() << "加载TIC:" << QFileInfo(data.getFilePath()).fileName();

        // 严格按照示例项目的方式：单文件时加载XIC
        QMap<uint32_t, QMap<QString, _PARAM_XIC *>> tempMap;

        // 按照示例项目的方式：使用局部变量而不是直接传递成员变量
        QList<std::vector<double>> DataOtherLines_Y;

        // 完全按照示例项目loadFileThreadTIC的调用方式
        // 注意：示例项目根据文件数量选择不同的重载版本
        int numFile = 1; // 当前是单文件处理

        // 完全按照示例项目的逻辑：先尝试单文件模式，失败后尝试多文件模式
        if ((numFile == 1) && (!cDataFileRead::loadFileTIC(period, data.indexArray, data.dataTIC_X, data.dataTIC_Y,
                                                           tempMap,          // 单文件时带XIC支持
                                                           DataOtherLines_Y, // 使用局部变量
                                                           data.streamHead, data.pageTIC, data.getFilePath())))
        {
            success = false;
        }
        else if (!cDataFileRead::loadFileTIC(period, data.indexArray, data.dataTIC_X, data.dataTIC_Y,
                                             // 多文件时不带XIC参数
                                             DataOtherLines_Y, // 使用局部变量
                                             data.streamHead, data.pageTIC, data.getFilePath()))
        {
            success = false;
        }
        else
        {
            success = true;
        }

        if (!success)
        {
            qDebug() << "cDataFileRead::loadFileTIC 调用失败";
            mutex.unlock();
            return false;
        }

        // 按照示例项目的方式：将局部变量赋值给成员变量
        data.otherLinesY = DataOtherLines_Y;

        // 严格按照示例项目的数据验证方式
        if ((data.dataTIC_X.size() != data.dataTIC_Y.size()) || (data.dataTIC_X.size() <= 0))
        {
            qDebug() << "TIC数据无效：X和Y数据大小不匹配或为空";
            mutex.unlock();
            return false;
        }

        // 严格按照示例项目的头文件处理方式
        QByteArray segment;
        QString pPropertyStr;
        QByteArray tuneFile;

        if (splitStreamHead(segment, data.streamHead, pPropertyStr, tuneFile) < 0)
        {
            qDebug() << "头文件解析失败";
            mutex.unlock();
            return false;
        }

        // 按照示例项目的方式存储解析后的数据
        data.mSegment.clear();
        data.mSegment.append(segment);

        // 存储XIC映射（按照示例项目的方式）
        if (!tempMap.isEmpty())
        {
            qDebug() << "TIC加载完成，包含" << tempMap.size() << "个XIC事件";
            data.xicMap = tempMap;
        }

        // 按照示例项目的方式初始化缓冲区
        data.tmpThreadBuffX.clear();
        data.tmpThreadBuffY.clear();
        data.tmpThreadBuffX.append(QList<std::vector<double>>());
        data.tmpThreadBuffY.append(QList<std::vector<double>>());

        qDebug() << "TIC数据加载成功，数据点数:" << data.dataTIC_X.size();

        // 转换数据格式
        convertTICData(data);

        //  设置对应的Dat文件路径
        QString currentDatFile = getDatFilePath(data.getFilePath());

        qDebug() << "TIC转换完成:" << data.timePoints.size() << "点," << data.frameIndices.size() << "帧";
    }
    catch (const std::exception &e)
    {
        qDebug() << "Exception in loadTICData: " << e.what();
        success = false;
    }
    catch (...)
    {
        qDebug() << "Unknown exception in loadTICData";
        success = false;
    }

    mutex.unlock();

    return success;
}

void DataReader::convertTICData(FileData &data)
{
    // 清空输出向量
    data.timePoints.clear();
    data.intensities.clear();
    data.frameIndices.clear();
    QVector<QPointF> ChartDataVec;
    QVector<double> vecX, vecY;
    // 转换时间点和强度数据
    for (size_t i = 0; i < data.dataTIC_X.size(); ++i)
    {
        data.timePoints.append(data.dataTIC_X[i]);
        data.intensities.append(data.dataTIC_Y[i]);
        QPointF point(data.dataTIC_X[i], data.dataTIC_Y[i]);
        ChartDataVec.append(point);
        vecX.append(data.dataTIC_X[i]);
        vecY.append(data.dataTIC_Y[i]);
    }

    // 使用 std::minmax_element 查找最小值和最大值的迭代器
    auto result = std::minmax_element(data.intensities.begin(), data.intensities.end());
    auto resultX = std::minmax_element(data.timePoints.begin(), data.timePoints.end());

    // 通过迭代器获取最小值和最大值
    double yMin = *result.first;
    double maxY = *result.second;
    double xMin = *resultX.first;
    double maxX = *resultX.second;

    // 使用新的数据结构，移除TicData结构体
    int eventId = 0; // 默认事件为0
    TicChartData *ticChartData = data.createTicData(eventId);

    // 直接设置TIC数据到ChartData对象
    ticChartData->setDataX(vecX);
    ticChartData->setDataY(vecY);
    ticChartData->setDataWithRange(ChartDataVec, xMin, maxX, yMin, maxY);
    ticChartData->setTitle("TIC数据");
    ticChartData->setYAxisRange(yMin, maxY * 1.02);

    // 转换帧索引
    for (size_t i = 0; i < data.indexArray.size(); ++i)
    {
        data.frameIndices.append(data.indexArray[i]);
    }
}

GlobalEnums::AvgMassStatus DataReader::getAvgMassStatus()
{
    return static_cast<GlobalEnums::AvgMassStatus>(avgMassStatus.loadAcquire());
}

void DataReader::setAvgMassStatus(GlobalEnums::AvgMassStatus newAvgMassStatus)
{
    avgMassStatus.storeRelease(static_cast<int>(newAvgMassStatus));
    qDebug() << "DataReader::setAvgMassStatus: 状态更新为" << static_cast<int>(newAvgMassStatus)
             << "(0=Calculating, 1=Ready, 2=Stop)，线程:" << QThread::currentThread();
}

bool DataReader::loadMassData(int eventId, qint64 frameIndex, FileData &data)
{
    QMutexLocker locker(&mutex); // 使用RAII方式锁定mutex

    bool successFlag = false;
    qDebug() << "开始加载MASS数据，帧索引:" << frameIndex << "线程:" << QThread::currentThread();

    // double maxMassY = data.MassChartData->m_maxY, minMassY = data.MassChartData->m_minY;
    // 暂时不使用缓存机制，因为读取速度很快，加上移除缓存也不会造成空间浪费
    //  if (data.MASS_Map.find(frameIndex) == data.MASS_Map.end()) {
    QString datFileName = getDatFilePath(data.getFilePath());
    try
    {
        // 创建临时存储缓冲区
        QByteArray massDataByteArray;
        QByteArray streamBody;

        // 检查文件是否存在
        QFile file(datFileName);
        if (!file.exists())
        {
            qDebug() << "【DataReader::loadMassData】错误: 质谱数据文件不存在:" << datFileName;
            mutex.unlock();
            return false;
        }

        // 在indexArray中查找frameIndex
        int actualIndexPos = -1;
        for (size_t i = 0; i < data.indexArray.size(); ++i)
        {
            if (data.indexArray[i] == frameIndex)
            {
                actualIndexPos = i;
                // qDebug() << "【DataReader::loadMassData】在indexArray中找到匹配的帧索引，位置:" << i;
                break;
            }
        }

        // 调用cDataFileRead::loadFileMass函数
        successFlag = cDataFileRead::loadFileMass(frameIndex, data.indexArray, massDataByteArray, streamBody, datFileName);

        // 如果直接使用frameIndex失败，尝试使用实际的索引值
        if (!successFlag && actualIndexPos >= 0)
        {
            // qDebug() << "MASS加载重试，使用位置索引";
            successFlag = cDataFileRead::loadFileMass(actualIndexPos, // 使用位置索引而不是帧索引
                                                      data.indexArray, massDataByteArray, streamBody, datFileName);
        }
        QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> mListSTRUCT_DATA;
        _STRUCT_PEAK tmpSTRUCT_PEAK;

        if (successFlag)
        {
            cParamValue::_Segment *pSegmentLIT = getSegment(0, data);
            if (pSegmentLIT)
            {
                // 按照示例项目的方式创建临时缓冲区
                QList<std::vector<double>> tmpThreadBuffX;
                QList<std::vector<double>> tmpThreadBuffY;

                // 严格按照示例项目的dataDisassembleFirst调用方式
                if (dataDisassembleFirst(massDataByteArray, pSegmentLIT, tmpThreadBuffX, tmpThreadBuffY, mListSTRUCT_DATA,
                                         true) == 0) // restart = true
                {
                    qDebug() << "数据解析失败";
                }

                uint32_t countEvt = mListSTRUCT_DATA.size();
                QMap<int, MassChartData *> MASS_Event_Map;
                for (uint32_t currentEvt = 0; currentEvt < countEvt; currentEvt++)
                {
                    QVector<double> x, y;
                    bool initX = false;
                    bool hasAvgMass = false;

                    // 检查是否有平均质谱数据且状态为Ready
                    if (DataReader::isRefExist && DataReader::getAvgMassStatus() == GlobalEnums::AvgMassStatus::Ready)
                    {
                        // 查找是否有平均质谱
                        if (DataReader::avgMassMap.contains(data.getFilePath()))
                        {
                            if (DataReader::avgMassMap[data.getFilePath()].contains(eventId))
                            {
                                // 开始减去平均质谱
                                std::tuple<QVector<double>, QVector<double>, bool, int> tuple = DataReader::avgMassMap.value(data.getFilePath()).value(eventId);
                                x = std::get<0>(tuple);
                                y = std::get<1>(tuple);
                                initX = std::get<2>(tuple);
                                if (!initX)
                                {
                                    qDebug() << QString("需要计算平均质谱，但是DataReader::avgMassMap没有%1 event:%2初始化x数据")
                                                    .arg(data.getFilePath())
                                                    .arg(eventId);
                                    return false;
                                }
                                hasAvgMass = true;
                                qDebug() << "DataReader::loadMassData: 找到平均质谱数据，将进行减法操作，事件ID:" << eventId;
                            }
                            else
                            {
                                qDebug() << QString("需要计算平均质谱，但是DataReader::avgMassMap没有%1 event:%2数据").arg(data.getFilePath()).arg(eventId);
                                return false;
                            }
                        }
                        else
                        {
                            qDebug() << QString("需要计算平均质谱，但是DataReader::avgMassMap没有%1数据").arg(data.getFilePath());
                            return false;
                        }
                    }

                    // 获取当前事件的m/z和强度数据（使用局部变量）
                    std::vector<double> &mzValues = tmpThreadBuffX[currentEvt];
                    std::vector<double> &intensities = tmpThreadBuffY[currentEvt];

                    qDebug() << "原大小:" << mzValues.size();
                    // qDebug() << "【DataReader::loadMassData】事件" << currentEvt << "的数据点数: mzValues=" << mzValues.size()
                    //          << ", intensities=" << intensities.size();
                    QVector<QPointF> massVec;
                    QVector<double> vecX, vecY;
                    double maxX = -1, minX = -1;
                    qDebug() << "111移除0前:" << mzValues.size();

                    // 移除为0的m/z
                    mzValues.erase(std::remove(mzValues.begin(), mzValues.end(), 0), mzValues.end());
                    qDebug() << "222移除0前:" << mzValues.size();

                    // 记录m/z范围
                    if (!mzValues.empty())
                    {
                        // 将m/z和强度值转换为QPointF数据点
                        for (size_t i = 0; i < mzValues.size(); ++i)
                        {
                            if (mzValues[i] != 0)
                            {
                                vecX.append(mzValues[i]);
                                vecY.append(intensities[i]);
                                if (intensities[i] == 0)
                                {
                                    // qDebug() << "添加强度为0";
                                }
                            }
                        }

                        // 检查平均质谱数组大小是否一致
                        if (hasAvgMass && vecY.size() != y.size())
                        {
                            qDebug() << "原始大小和平均质谱数组大小不一致" << vecY.size() << x.size() << y.size();
                            return false;
                        }

                        for (int j = 0; j < vecY.size(); j++)
                        {
                            // 如果有平均质谱数据，则进行减法操作
                            if (hasAvgMass)
                            {
                                double before = vecY[j];
                                vecY.replace(j, before - y.at(j));
                                // qDebug() << "减去前后" << j << ":" << before << y.at(j) << "," << vecY[j];
                            }
                            massVec << QPointF(vecX[j], vecY[j]);
                        }

                        // 使用新的数据结构，直接设置到TicChartData的MassData中
                        TicChartData *ticData = data.getTicData(eventId);
                        if (!ticData)
                        {
                            ticData = data.createTicData(eventId);
                        }

                        MassChartData *massChartData = ticData->getMassData();
                        if (!massChartData)
                        {
                            massChartData = ticData->createMassData();
                        }

                        // 计算范围
                        double maxX = *(mzValues.end() - 1);
                        double minX = *(mzValues.begin());
                        auto result = std::minmax_element(intensities.begin(), intensities.end());
                        double minY = *result.first;
                        double maxY = *result.second;

                        // 设置MASS数据（setMassDataComplete已经包含了基类数据设置）
                        massChartData->setMassDataComplete(massVec, minX, maxX, minY, maxY, "MASS数据");

                        // 设置X/Y向量数据（用于其他用途）
                        massChartData->setDataX(vecX);
                        massChartData->setDataY(vecY);

                        // qDebug() << "MASS数据设置完成，点数:" << massVec.size() << "，范围: X[" << minX << "," << maxX << "], Y[" << minY << "," << maxY <<
                        // "]";
                    }
                }

                // 移除MASS_Map.insert，现在MASS数据由TicChartData管理

                // qDebug() << "MASS读取完毕";
            }
        }
        else
        {
            qDebug() << "MASS加载失败，帧索引:" << frameIndex;
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MASS加载异常:" << e.what();
        successFlag = false;
    }
    catch (...)
    {
        qDebug() << "【DataReader::loadMassData】Unknown exception" << "线程:" << QThread::currentThread();
        successFlag = false;
    }
    // }
    /*else {
        qDebug() << "帧索引已存在，直接使用缓存:" << frameIndex << "线程:" << QThread::currentThread();
        auto it = data.MASS_Map.find(frameIndex);
        if (it != data.MASS_Map.end()) {
            // data.MassChartData->setData(it.value());
            successFlag = true;
        } else {
            successFlag = false;
        }
    }*/

    // qDebug() << "完成加载MASS数据，帧索引:" << frameIndex << "结果:" << successFlag << "线程:" << QThread::currentThread();
    return successFlag;
}

bool DataReader::loadMassDataForAvg(int eventId, QVector<int> frameIndexVec, FileData &data)
{
    QMutexLocker locker(&mutex); // 使用RAII方式锁定mutex

    bool successFlag = false;

    foreach (int frameIndex, frameIndexVec)
    {
        if (getAvgMassStatus() == GlobalEnums::AvgMassStatus::Stop)
        {
            // 如果状态是停止计算则立刻退出
            qDebug() << "停止计算";
            return false;
        }

        // qDebug() << "开始加载MASS数据，帧索引:" << frameIndex << "线程:" << QThread::currentThread();

        // double maxMassY = data.MassChartData->m_maxY, minMassY = data.MassChartData->m_minY;

        // 新架构不使用缓存，直接加载数据
        if (true) // 总是重新加载，因为新架构不使用MASS_Map缓存
        {
            QString datFileName = getDatFilePath(data.getFilePath());
            try
            {
                // 创建临时存储缓冲区
                QByteArray massDataByteArray;
                QByteArray streamBody;

                // 检查文件是否存在
                QFile file(datFileName);
                if (!file.exists())
                {
                    qDebug() << "【DataReader::loadMassData】错误: 质谱数据文件不存在:" << datFileName;
                    mutex.unlock();
                    return false;
                }

                // 在indexArray中查找frameIndex
                int actualIndexPos = -1;
                for (size_t i = 0; i < data.indexArray.size(); ++i)
                {
                    if (data.indexArray[i] == frameIndex)
                    {
                        actualIndexPos = i;
                        // qDebug() << "【DataReader::loadMassData】在indexArray中找到匹配的帧索引，位置:" << i;
                        break;
                    }
                }

                // 调用cDataFileRead::loadFileMass函数
                successFlag = cDataFileRead::loadFileMass(frameIndex, data.indexArray, massDataByteArray, streamBody, datFileName);

                // 如果直接使用frameIndex失败，尝试使用实际的索引值
                if (!successFlag && actualIndexPos >= 0)
                {
                    // qDebug() << "MASS加载重试，使用位置索引";
                    successFlag = cDataFileRead::loadFileMass(actualIndexPos, // 使用位置索引而不是帧索引
                                                              data.indexArray, massDataByteArray, streamBody, datFileName);
                }
                QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> mListSTRUCT_DATA;
                _STRUCT_PEAK tmpSTRUCT_PEAK;

                if (successFlag)
                {
                    cParamValue::_Segment *pSegmentLIT = getSegment(0, data);
                    if (pSegmentLIT)
                    {
                        // 按照示例项目的方式创建临时缓冲区
                        QList<std::vector<double>> tmpThreadBuffX;
                        QList<std::vector<double>> tmpThreadBuffY;

                        if (dataDisassembleFirst(massDataByteArray, pSegmentLIT, tmpThreadBuffX, tmpThreadBuffY, mListSTRUCT_DATA, true) == 0)
                        {
                            qDebug() << "dataDisassembleFirst读取失败";
                        }

                        uint32_t countEvt = mListSTRUCT_DATA.size();
                        for (uint32_t currentEvt = 0; currentEvt < countEvt; currentEvt++)
                        {
                            // 获取当前事件的m/z和强度数据（使用局部变量）
                            std::vector<double> &mzValues = tmpThreadBuffX[currentEvt];
                            std::vector<double> &intensities = tmpThreadBuffY[currentEvt];
                            // qDebug() << "原大小:" << mzValues.size();
                            // qDebug() << "【DataReader::loadMassData】事件" << currentEvt << "的数据点数: mzValues=" << mzValues.size()
                            //          << ", intensities=" << intensities.size();
                            QVector<double> vecX, vecY;
                            qDebug() << "移除0前:" << mzValues.size();
                            int count000 = 0;
                            foreach (double x, mzValues)
                            {
                                if (x == 0)
                                {
                                    ++count000;
                                }
                            }
                            // 移除为0的m/z
                            for (auto it = mzValues.begin(); it != mzValues.end();)
                            {
                                if (*it == 0)
                                {
                                    it = mzValues.erase(it); // 移除并返回下一个迭代器
                                    // qDebug() << "移除了";
                                }
                                else
                                {
                                    ++it; // 仅在未移除时递增
                                }
                            }
                            // mzValues.erase(std::remove(mzValues.begin(), mzValues.end(), 0), mzValues.end());
                            qDebug() << "移除0后:" << mzValues.size() << count000;

                            QMutex mutex;
                            mutex.lock();
                            if (!avgMassMap.contains(data.getFilePath()))
                            {
                                // 如果不包括则先创建一个
                                QMap<int, std::tuple<QVector<double>, QVector<double>, bool, int>> tempMap;
                                tempMap.insert(eventId, std::make_tuple(QVector<double>(), QVector<double>(), false, 0));
                                avgMassMap.insert(data.getFilePath(), tempMap);
                            }
                            else
                            {
                                // 如果包括了则获取内部Map
                                auto it = avgMassMap.find(data.getFilePath());
                                // 判断是否存在当前事件的子Map
                                if (!it->contains(eventId))
                                {
                                    // 如果不存在则添加子Map
                                    it->insert(eventId, std::make_tuple(QVector<double>(), QVector<double>(), false, 0));
                                }
                            }

                            // 获取对应的平均数组引用，并追加到对应位置
                            QVector<double> &tempVecX = std::get<0>(avgMassMap.find(data.getFilePath())->find(eventId).value());
                            QVector<double> &tempVecY = std::get<1>(avgMassMap.find(data.getFilePath())->find(eventId).value());
                            bool &isInitX = std::get<2>(avgMassMap.find(data.getFilePath())->find(eventId).value());
                            int &count = std::get<3>(avgMassMap.find(data.getFilePath())->find(eventId).value());
                            //  记录m/z范围
                            if (!mzValues.empty())
                            {
                                if (tempVecY.size() != mzValues.size())
                                {
                                    tempVecY.resize(mzValues.size());
                                    tempVecX.resize(mzValues.size());
                                }
                                int count0 = 0;
                                // 将m/z和强度值转换为QPointF数据点
                                for (size_t i = 0; i < mzValues.size(); ++i)
                                {
                                    if (mzValues[i] != 0)
                                    {
                                        if (!isInitX)
                                        {
                                            tempVecX[i] = mzValues[i];
                                            // qDebug() << "x点:" << mzValues[i];
                                        }
                                        tempVecY[i] += intensities[i];
                                    }
                                    else
                                    {
                                        ++count0;
                                    }
                                }
                                qDebug() << "count0" << count0;
                                count++;
                                isInitX = true;
                            }

                            mutex.unlock();
                        }

                        // qDebug() << "读取完毕了";
                    }
                }
                else
                {
                    qDebug() << "MASS加载失败";
                }
            }
            catch (const std::exception &e)
            {
                qDebug() << "MASS加载异常:" << e.what();
                successFlag = false;
            }
            catch (...)
            {
                qDebug() << "【DataReader::loadMassData】Unknown exception" << "线程:" << QThread::currentThread();
                successFlag = false;
            }
        }
        else
        {
            // TODO: 重构平均质谱处理逻辑，使用新的TicChartData结构
            // 暂时注释掉旧的MASS_Map缓存逻辑
            qDebug() << "帧索引已存在，但新架构不使用缓存，重新加载:" << frameIndex << "线程:" << QThread::currentThread();

            // 直接重新加载数据，因为新架构不使用缓存
            successFlag = false; // 强制重新加载

            // TODO: 重构平均质谱处理逻辑，使用新的TicChartData结构
            // 暂时注释掉旧的平均质谱逻辑，因为依赖MASS_Map
            qDebug() << "平均质谱处理逻辑需要重构，暂时跳过";
            successFlag = false; // 暂时返回false，等待重构
        }
    }
    // qDebug() << "完成加载MASS数据，帧索引:" << frameIndex << "结果:" << successFlag << "线程:" << QThread::currentThread();
    return successFlag;
}

// 新增：批量加载XIC数据的结构体
struct XicDataPoint
{
    double timePoint;
    double intensity;
    bool success;

    XicDataPoint(double t = 0.0, double i = 0.0, bool s = false) : timePoint(t), intensity(i), success(s) {}
};

bool DataReader::loadMassDataForXic(qint64 frameIndex, FileData &data, double timePoint, double timePointIndex, double mz)
{
    // qDebug() << "DataReader::loadMassDataForXic: 开始处理，线程:" << QThread::currentThread();

    // 第一阶段：在工作线程中处理文件数据（不涉及Qt对象创建）
    QMutexLocker locker(&mutex);
    bool successFlag = false;
    double extractedIntensity = 0.0;

    // 简化对象检查，减少阻塞调用
    int eventId = 0;

    // 先检查对象是否已存在，避免不必要的主线程调用
    TicChartData *ticData = data.getTicData(eventId);
    XicChartData *xicData = nullptr;
    if (ticData)
    {
        // 获取第一个XIC数据（如果有多个XIC，这里需要根据具体需求选择）
        QVector<XicChartData *> xicList = ticData->getXicDataList();
        if (!xicList.isEmpty())
        {
            xicData = xicList.first();
        }
    }

    // 只有在对象不存在时才调用主线程创建
    if (!ticData || !xicData)
    {
        bool objectsReady = false;
        QMetaObject::invokeMethod(
            QCoreApplication::instance(),
            [&]()
            {
                try
                {
                    if (!ticData)
                    {
                        ticData = data.createTicData(eventId);
                        qDebug() << "DataReader::loadMassDataForXic: 在主线程中创建TIC数据";
                    }

                    if (!xicData)
                    {
                        QUuid xicId = ticData->createXicData();
                        if (!xicId.isNull())
                        {
                            xicData = ticData->getXicData(xicId);
                        }
                        qDebug() << "DataReader::loadMassDataForXic: 在主线程中创建XIC数据";
                    }

                    objectsReady = true;
                }
                catch (...)
                {
                    qDebug() << "DataReader::loadMassDataForXic: 主线程对象创建失败";
                    objectsReady = false;
                }
            },
            Qt::BlockingQueuedConnection);

        if (!objectsReady)
        {
            return false;
        }
    }

    QString datFileName = getDatFilePath(data.getFilePath());
    try
    {
        // 创建临时存储缓冲区
        QByteArray massDataByteArray;
        QByteArray streamBody;
        // 检查文件是否存在
        QFile file(datFileName);
        if (!file.exists())
        {
            qDebug() << "【DataReader::loadMassData】错误: 质谱数据文件不存在:" << datFileName;
            mutex.unlock();
            return false;
        }

        // 在indexArray中查找frameIndex
        int actualIndexPos = -1;
        for (size_t i = 0; i < data.indexArray.size(); ++i)
        {
            if (data.indexArray[i] == frameIndex)
            {
                actualIndexPos = i;
                // qDebug() << "【DataReader::loadMassData】在indexArray中找到匹配的帧索引，位置:" << i;
                break;
            }
        }

        // 调用cDataFileRead::loadFileMass函数
        successFlag = cDataFileRead::loadFileMass(frameIndex, data.indexArray, massDataByteArray, streamBody, datFileName);

        // 如果直接使用frameIndex失败，尝试使用实际的索引值
        if (!successFlag && actualIndexPos >= 0)
        {
            successFlag = cDataFileRead::loadFileMass(actualIndexPos, // 使用位置索引而不是帧索引
                                                      data.indexArray, massDataByteArray, streamBody, datFileName);
        }

        QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> mListSTRUCT_DATA;
        _STRUCT_PEAK tmpSTRUCT_PEAK;

        if (successFlag)
        {
            // qDebug() << "data segment size " << data.mSegment.size();
            cParamValue::_Segment *pSegmentLIT = getSegment(0, data);
            // qDebug() << "getSegTimeMs" << cParamValue::_Segment::getSegTimeMs(pSegmentLIT);
            if (pSegmentLIT)
            {
                // 按照示例项目的方式创建临时缓冲区
                QList<std::vector<double>> tmpThreadBuffX;
                QList<std::vector<double>> tmpThreadBuffY;

                if (dataDisassembleFirst(massDataByteArray, pSegmentLIT, tmpThreadBuffX, tmpThreadBuffY, mListSTRUCT_DATA, true) == 0)
                {
                    qDebug() << "dataDisassembleFirst读取失败";
                }

                uint32_t countEvt = mListSTRUCT_DATA.size();
                for (uint32_t currentEvt = 0; currentEvt < countEvt; currentEvt++)
                {
                    // 获取当前事件的m/z和强度数据（使用局部变量）
                    std::vector<double> &mzValues = tmpThreadBuffX[currentEvt];
                    std::vector<double> &intensities = tmpThreadBuffY[currentEvt];
                    // qDebug() << "【DataReader::loadMassData】事件" << currentEvt << "的数据点数: mzValues=" << mzValues.size()
                    //          << ", intensities=" << intensities.size();
                    // 移除为0的m/z
                    mzValues.erase(std::remove(mzValues.begin(), mzValues.end(), 0), mzValues.end());
                    // qDebug() << "m/z = " << mz;

                    // 记录m/z范围
                    if (!mzValues.empty())
                    {
                        double sumMz = 0;
                        // 改为选取阈值范围内的距离mz最近的点
                        double min = std::numeric_limits<double>::max();
                        for (size_t i = 0; i < mzValues.size(); ++i)
                        {
                            double diff = (mzValues[i] - mz);
                            if (qAbs(diff) < xicThresholdValue)
                            {
                                if (diff < min)
                                {
                                    min = diff;
                                    sumMz = intensities[i];
                                }
                                // qDebug() << "xic:" << mzValues[i] << mz << diff << QPointF(timePoint, intensities[i]);
                            }
                        }
                        // 将m/z和强度值转换为QPointF数据点
                        // for (size_t i = 0; i < mzValues.size(); ++i) {
                        //     double diff = (mzValues[i] - mz);
                        //     if (qAbs(diff) < xicThresholdValue) {
                        //         sumMz += intensities[i];
                        //         // qDebug() << "xic:" << mzValues[i] << mz << diff << QPointF(timePoint, intensities[i]);
                        //     }
                        // }

                        // 旧的XIC数据操作代码已移除，现在使用线程安全的方式在主线程中设置数据

                        // 提取强度值用于后续的线程安全设置
                        extractedIntensity = sumMz;
                        successFlag = true;

                        // qDebug() << "DataReader::loadMassDataForXic: 更新XIC数据点[" << timePointIndex << "] = (" << timePoint << "," << sumMz << ")";

                        // qDebug() << "添加" << timePointIndex << QPointF(timePoint, sumMz);
                    }
                }
            }
        }
        else
        {
            qDebug() << "MASS加载失败";
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MASS加载异常:" << e.what();
        successFlag = false;
    }
    catch (...)
    {
        qDebug() << "【DataReader::loadMassData】Unknown exception" << "线程:" << QThread::currentThread();
        successFlag = false;
    }

    // 第二阶段：不再在这里设置XIC数据，而是返回数据供批量处理
    // 移除了Qt::BlockingQueuedConnection调用，提高性能

    return successFlag;
}

// 新增：批量加载XIC数据的优化版本
bool DataReader::loadXicDataBatch(FileData &data, int eventId, double mz)
{
    if (data.TicMap.isEmpty())
    {
        return false;
    }

    qDebug() << "XIC批量加载开始，mz:" << mz;

    // 第一阶段：在主线程中预创建对象（只调用一次）
    bool preCreationSuccess = false;
    QMetaObject::invokeMethod(
        QCoreApplication::instance(),
        [&]()
        {
            try
            {
                TicChartData *ticData = data.getTicData(eventId);
                if (!ticData)
                {
                    ticData = data.createTicData(eventId);
                    // qDebug() << "创建TIC数据";
                }

                // 总是创建新的XIC数据，不复用已有的
                QUuid xicId = ticData->createXicData();
                XicChartData *xicData = nullptr;
                if (!xicId.isNull())
                {
                    xicData = ticData->getXicData(xicId);
                    // qDebug() << "创建XIC数据，ID:" << xicId.toString();
                }
                else
                {
                    qDebug() << "XIC创建失败";
                    preCreationSuccess = false;
                    return;
                }

                xicData->clearDataThreadSafe();
                preCreationSuccess = true;
                // qDebug() << "XIC对象预创建完成";
            }
            catch (...)
            {
                qDebug() << "XIC对象预创建失败";
                preCreationSuccess = false;
            }
        },
        Qt::BlockingQueuedConnection);

    if (!preCreationSuccess)
    {
        return false;
    }

    // 第二阶段：在工作线程中批量处理所有数据
    QMutexLocker mainLocker(&mutex);

    QElapsedTimer timer;
    timer.start();

    QVector<QPointF> ticPoints;
    if (!data.TicMap.isEmpty())
    {
        ticPoints = (*data.TicMap.begin())->getDataThreadSafe();
        // qDebug() << "TIC数据点数:" << ticPoints.size();
    }
    else
    {
        qDebug() << "XIC加载失败：TicMap为空";
        return false;
    }

    int totalPoints = ticPoints.size();

    if (totalPoints <= 0)
    {
        qDebug() << "XIC加载失败：TIC数据为空";
        return false;
    }

    qDebug() << "XIC处理" << totalPoints << "个数据点";

    // 使用分批处理，减少单次处理时间
    QVector<XicDataPoint> xicDataPoints(totalPoints);

    mainLocker.unlock();

    // qDebug() << "开始分批处理数据点";

    const int batchSize = 50; // 每批处理50个数据点
    int processedCount = 0;

    // 分批处理所有数据点
    for (int batchStart = 0; batchStart < totalPoints; batchStart += batchSize)
    {
        int batchEnd = qMin(batchStart + batchSize, totalPoints);

        // 处理当前批次
        for (int i = batchStart; i < batchEnd; ++i)
        {
            QPointF point = ticPoints[i];
            double timePoint = point.x();

            // 找到对应的帧索引
            int frameIndex = -1;
            for (int j = 0; j < data.timePoints.size(); ++j)
            {
                if (qAbs(data.timePoints[j] - timePoint) < 0.0001)
                {
                    frameIndex = j;
                    break;
                }
            }

            if (frameIndex < 0 || frameIndex >= data.frameIndices.size())
            {
                xicDataPoints[i] = XicDataPoint(timePoint, 0.0, false);
                continue;
            }

            qint64 actualFrameIndex = data.frameIndices[frameIndex];

            // 直接在当前线程中处理数据
            double extractedIntensity = 0.0;
            bool success = this->extractIntensityForMz(actualFrameIndex, data, mz, extractedIntensity);

            xicDataPoints[i] = XicDataPoint(timePoint, extractedIntensity, success);
            processedCount++;
        }

        // 每批处理完后让出CPU时间，避免长时间占用
        if (batchEnd < totalPoints)
        {
            QThread::msleep(1); // 短暂休息1ms
        }

        // 输出进度
        // 每处理100个点输出一次进度
        if (processedCount % 100 == 0)
        {
            qDebug() << "XIC进度:" << processedCount << "/" << totalPoints;
        }
    }

    qDebug() << "XIC数据点处理完成，设置数据";

    // 在工作线程中直接设置XIC数据，避免线程切换
    this->setXicDataInWorkerThread(data, eventId, xicDataPoints, timer.elapsed());

    return true;
}

// 实现：提取单个m/z的强度值
bool DataReader::extractIntensityForMz(qint64 frameIndex, FileData &data, double mz, double &extractedIntensity)
{
    QMutexLocker locker(&mutex);
    extractedIntensity = 0.0;

    // 减少调试输出频率，只在特定条件下输出
    static int debugCounter = 0;
    bool shouldDebug = (debugCounter % 50 == 0); // 每50次输出一次
    debugCounter++;

    if (shouldDebug)
    {
        qDebug() << "DataReader::extractIntensityForMz: 开始提取，frameIndex:" << frameIndex << "，m/z:" << mz;
    }

    QString datFileName = getDatFilePath(data.getFilePath());
    try
    {
        QByteArray massDataByteArray;
        QByteArray streamBody; // 修复：使用QByteArray而不是QDataStream

        // 查找实际的索引位置
        int actualIndexPos = -1;
        for (int i = 0; i < data.indexArray.size(); i++)
        {
            if (data.indexArray[i] == frameIndex)
            {
                actualIndexPos = i;
                break;
            }
        }

        bool successFlag = cDataFileRead::loadFileMass(frameIndex, data.indexArray, massDataByteArray, streamBody, datFileName);

        if (!successFlag && actualIndexPos >= 0)
        {
            if (shouldDebug)
            {
                qDebug() << "DataReader::extractIntensityForMz: 第一次加载失败，尝试使用actualIndexPos:" << actualIndexPos;
            }
            successFlag = cDataFileRead::loadFileMass(actualIndexPos, data.indexArray, massDataByteArray, streamBody, datFileName);
        }

        if (shouldDebug)
        {
            qDebug() << "DataReader::extractIntensityForMz: 文件加载结果:" << successFlag;
        }

        if (successFlag)
        {
            QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> mListSTRUCT_DATA;
            cParamValue::_Segment *pSegmentLIT = getSegment(0, data);

            // 按照示例项目的方式创建临时缓冲区
            QList<std::vector<double>> tmpThreadBuffX;
            QList<std::vector<double>> tmpThreadBuffY;

            if (pSegmentLIT && dataDisassembleFirst(massDataByteArray, pSegmentLIT, tmpThreadBuffX, tmpThreadBuffY, mListSTRUCT_DATA, true) != 0)
            {
                uint32_t countEvt = mListSTRUCT_DATA.size();
                if (shouldDebug)
                {
                    qDebug() << "DataReader::extractIntensityForMz: 数据解析成功，事件数:" << countEvt;
                }

                for (uint32_t currentEvt = 0; currentEvt < countEvt; currentEvt++)
                {
                    std::vector<double> &mzValues = tmpThreadBuffX[currentEvt];
                    std::vector<double> &intensities = tmpThreadBuffY[currentEvt];

                    mzValues.erase(std::remove(mzValues.begin(), mzValues.end(), 0), mzValues.end());

                    if (shouldDebug)
                    {
                        qDebug() << "DataReader::extractIntensityForMz: 事件" << currentEvt << "，m/z值数量:" << mzValues.size();
                    }

                    if (!mzValues.empty())
                    {
                        double maxIntensity = 0;
                        int matchCount = 0;
                        for (size_t i = 0; i < mzValues.size(); ++i)
                        {
                            double diff = qAbs(mzValues[i] - mz);
                            if (diff <= xicThresholdValue)
                            {
                                // 使用范围内的最高强度值，而不是累加
                                if (intensities[i] > maxIntensity)
                                {
                                    maxIntensity = intensities[i];
                                }
                                matchCount++;
                            }
                        }
                        if (shouldDebug)
                        {
                            qDebug() << "DataReader::extractIntensityForMz: 匹配的m/z数量:" << matchCount << "，最高强度:" << maxIntensity;
                        }
                        extractedIntensity = maxIntensity;
                        return true;
                    }
                    else if (shouldDebug)
                    {
                        qDebug() << "DataReader::extractIntensityForMz: 事件" << currentEvt << "的m/z值为空";
                    }
                }
            }
            else if (shouldDebug)
            {
                qDebug() << "DataReader::extractIntensityForMz: 数据解析失败";
            }
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "DataReader::extractIntensityForMz Exception: " << e.what() << "，frameIndex:" << frameIndex;
        return false;
    }
    catch (...)
    {
        qDebug() << "DataReader::extractIntensityForMz Unknown exception，frameIndex:" << frameIndex;
        return false;
    }

    qDebug() << "DataReader::extractIntensityForMz: 提取失败，frameIndex:" << frameIndex << "，m/z:" << mz;
    return false;
}

// 新增：在工作线程中设置XIC数据
void DataReader::setXicDataInWorkerThread(FileData &data, int eventId, const QVector<XicDataPoint> &xicDataPoints, qint64 elapsedTime)
{
    qDebug() << "DataReader::setXicDataInWorkerThread: 开始在工作线程中处理" << xicDataPoints.size() << "个数据点";

    // 在工作线程中准备批量数据
    QVector<double> xValues, yValues;
    int successCount = 0;
    int totalCount = xicDataPoints.size();

    for (const XicDataPoint &point : xicDataPoints)
    {
        if (point.success)
        {
            xValues.append(point.timePoint);
            yValues.append(point.intensity);
            successCount++;
        }
    }

    qDebug() << "DataReader::setXicDataInWorkerThread: 成功数据点:" << successCount << "，总数据点:" << totalCount;

    // 在工作线程中直接设置数据（线程安全）
    try
    {
        TicChartData *ticData = data.getTicData(eventId);
        if (ticData && ticData->hasXicData())
        {
            // 获取第一个XIC数据（如果有多个XIC，这里需要根据具体需求选择）
            QVector<XicChartData *> xicList = ticData->getXicDataList();
            XicChartData *xicData = xicList.isEmpty() ? nullptr : xicList.first();

            // 直接在工作线程中设置数据（setDataThreadSafe是线程安全的）
            if (!xValues.isEmpty())
            {
                xicData->setDataThreadSafe(xValues, yValues);
                xicData->setXicTitle("XIC数据");

                qDebug() << "DataReader::setXicDataInWorkerThread: 在工作线程中设置XIC数据完成";
                qDebug() << "  数据点数:" << xValues.size() << "，成功率:" << (double)successCount / totalCount * 100 << "%";
                qDebug() << "  总耗时:" << elapsedTime << "毫秒，平均:" << (double)elapsedTime / totalCount << "毫秒/点";
            }
            else
            {
                qDebug() << "DataReader::setXicDataInWorkerThread: 警告：没有成功的数据点，所有" << totalCount << "个数据点都失败了";

                // 即使没有数据也要设置空数据，避免后续问题
                xicData->clearDataThreadSafe();
                xicData->setXicTitle("XIC数据(无数据)");
            }
        }
    }
    catch (...)
    {
        qDebug() << "DataReader::setXicDataInWorkerThread: 在工作线程中设置XIC数据时异常";
    }
}

// 实现：批量设置XIC数据（保留原函数以兼容）
void DataReader::setXicDataBatch(FileData &data, int eventId, const QVector<XicDataPoint> &xicDataPoints, qint64 elapsedTime)
{
    QMetaObject::invokeMethod(
        QCoreApplication::instance(),
        [&]()
        {
            try
            {
                TicChartData *ticData = data.getTicData(eventId);
                if (ticData && ticData->hasXicData())
                {
                    // 获取第一个XIC数据（如果有多个XIC，这里需要根据具体需求选择）
                    QVector<XicChartData *> xicList = ticData->getXicDataList();
                    XicChartData *xicData = xicList.isEmpty() ? nullptr : xicList.first();

                    // 准备批量数据
                    QVector<double> xValues, yValues;
                    int successCount = 0;
                    int totalCount = xicDataPoints.size();

                    qDebug() << "DataReader::setXicDataBatch: 开始处理" << totalCount << "个数据点";

                    for (const XicDataPoint &point : xicDataPoints)
                    {
                        if (point.success)
                        {
                            xValues.append(point.timePoint);
                            yValues.append(point.intensity);
                            successCount++;
                        }
                        else
                        {
                            // 调试：记录失败的数据点
                            qDebug() << "DataReader::setXicDataBatch: 数据点失败，时间:" << point.timePoint;
                        }
                    }

                    qDebug() << "DataReader::setXicDataBatch: 成功数据点:" << successCount << "，总数据点:" << totalCount;

                    // 一次性设置所有数据
                    if (!xValues.isEmpty())
                    {
                        xicData->setDataThreadSafe(xValues, yValues);
                        xicData->setXicTitle("XIC数据");

                        qDebug() << "DataReader::setXicDataBatch: 批量设置XIC数据完成";
                        qDebug() << "  数据点数:" << xValues.size() << "，成功率:" << (double)successCount / totalCount * 100 << "%";
                        qDebug() << "  总耗时:" << elapsedTime << "毫秒，平均:" << (double)elapsedTime / totalCount << "毫秒/点";
                    }
                    else
                    {
                        qDebug() << "DataReader::setXicDataBatch: 警告：没有成功的数据点，所有" << totalCount << "个数据点都失败了";

                        // 即使没有数据也要设置空数据，避免后续问题
                        xicData->clearDataThreadSafe();
                        xicData->setXicTitle("XIC数据(无数据)");
                    }
                }
            }
            catch (...)
            {
                qDebug() << "DataReader::setXicDataBatch: 设置XIC数据时异常";
            }
        },
        Qt::QueuedConnection); // 改回异步调用，避免阻塞工作线程
}

bool DataReader::loadXicData(FileData &data, int eventId, double mz)
{
    if (data.TicMap.isEmpty())
    {
        return false;
    }

    // qDebug() << "XIC加载开始";

    // 第一阶段：在主线程中预创建所有必要的对象
    bool preCreationSuccess = false;
    QMetaObject::invokeMethod(
        QCoreApplication::instance(),
        [&]()
        {
            try
            {
                TicChartData *ticData = data.getTicData(eventId);
                if (!ticData)
                {
                    ticData = data.createTicData(eventId);
                    qDebug() << "DataReader::loadXicData: 创建TIC数据";
                }

                // 获取第一个XIC数据（如果有多个XIC，这里需要根据具体需求选择）
                XicChartData *xicData = nullptr;
                QVector<XicChartData *> xicList = ticData->getXicDataList();
                if (!xicList.isEmpty())
                {
                    xicData = xicList.first();
                }

                if (!xicData)
                {
                    QUuid xicId = ticData->createXicData();
                    if (!xicId.isNull())
                    {
                        xicData = ticData->getXicData(xicId);
                        qDebug() << "DataReader::loadXicData: 创建XIC数据";
                    }
                }

                // 清空现有XIC数据，准备接收新数据
                xicData->clearDataThreadSafe();

                preCreationSuccess = true;
                qDebug() << "DataReader::loadXicData: 对象预创建完成";
            }
            catch (...)
            {
                qDebug() << "DataReader::loadXicData: 对象预创建失败";
                preCreationSuccess = false;
            }
        },
        Qt::BlockingQueuedConnection);

    if (!preCreationSuccess)
    {
        return false;
    }

    QMutexLocker mainLocker(&mutex);

    // 开始计时
    QElapsedTimer timer;
    timer.start();

    // 获取所有时间点（使用线程安全方法）
    QVector<QPointF> ticPoints;
    if (!data.TicMap.isEmpty())
    {
        ticPoints = (*data.TicMap.begin())->getDataThreadSafe();
    }
    int totalPoints = ticPoints.size();
    qDebug() << "开始加载所有MASS数据，共" << totalPoints << "个时间点" << "线程:" << QThread::currentThread();

    if (totalPoints <= 0)
    {
        qDebug() << "没有可加载的时间点";
        return false;
    }

    // 初始化计数器
    data.readMassCount = 0;
    QAtomicInt successCount = 0;
    QMutex countMutex;

    // 创建等待信号量，用于等待所有任务完成
    QSemaphore semaphore;

    // 释放主mutex，让其他线程可以访问
    mainLocker.unlock();

    // 对每个时间点并行加载Xic数据
    for (int i = 0; i < totalPoints; ++i)
    {
        QPointF point = ticPoints[i];
        double timePoint = point.x();

        // 找到对应的帧索引
        int frameIndex = -1;
        for (int j = 0; j < data.timePoints.size(); ++j)
        {
            if (qAbs(data.timePoints[j] - timePoint) < 0.0001)
            {
                frameIndex = j;
                break;
            }
        }

        if (frameIndex < 0 || frameIndex >= data.frameIndices.size())
        {
            qDebug() << "时间点" << timePoint << "没有对应的帧索引" << "线程:" << QThread::currentThread();
            countMutex.lock();
            data.readMassCount++;
            countMutex.unlock();
            semaphore.release(1); // 释放一个信号量
            continue;
        }

        qint64 actualFrameIndex = data.frameIndices[frameIndex];

        // 在线程池中加载MASS数据
        TaskManager::instance()->run([this, eventId, timePoint, mz, actualFrameIndex, &data, &successCount, &countMutex, &semaphore, totalPoints, i, timer]()
                                     {
            // qDebug() << "开始处理任务" << i << "帧索引:" << actualFrameIndex << "线程:" << QThread::currentThread();

            // 加载MASS数据
            bool success = this->loadMassDataForXic(actualFrameIndex, data, timePoint, i, mz);
            // 更新计数
            countMutex.lock();
            if (success) {
                successCount++;
            }
            data.readMassCount++;

            // int completedCount = data.readMassCount;
            // qreal progress = (qreal)completedCount / totalPoints * 100.0;
            // qDebug() << "已完成" << completedCount << "个Xic数据加载，进度" << QString::number(progress, 'f', 2) << "% 线程:" << QThread::currentThread();

            // 检查是否所有数据都已加载
            if (data.readMassCount == totalPoints) {
                qint64 elapsed = timer.elapsed();

                if (successCount == totalPoints) {
                    // 获取XIC数据并设置到ChartData
                    TicChartData *ticData = data.getTicData(eventId);
                    if (ticData && ticData->hasXicData()) {
                        // 获取第一个XIC数据（如果有多个XIC，这里需要根据具体需求选择）
                        QVector<XicChartData *> xicList = ticData->getXicDataList();
                        XicChartData *xicData = xicList.isEmpty() ? nullptr : xicList.first();
                        QVector<QPointF> xicPoints = xicData->getXicData();

                        QVector<double> vecX, vecY;
                        for (const QPointF &point : xicPoints) {
                            vecX.append(point.x());
                            vecY.append(point.y());
                        }

                        xicData->setDataX(vecX);
                        xicData->setDataY(vecY);
                        xicData->setXicTitle("XIC数据");

                        // 确保基类数据也正确设置
                        double minX = xicData->getMinX();
                        double maxX = xicData->getMaxX();
                        double minY = xicData->getMinY();
                        double maxY = xicData->getMaxY();
                        xicData->setDataWithRange(xicPoints, minX, maxX, minY, maxY);

                        qDebug() << "DataReader::loadXicData: XIC数据设置完成，数据点数:" << xicPoints.size() << "，范围: X[" << minX << "," << maxX << "], Y["
                                 << minY << "," << maxY << "]";

                        qDebug() << "XIC加载完成:" << successCount << "/" << totalPoints << "，耗时:" << elapsed << "ms";
                    }
                } else {
                    qDebug() << "XIC加载完成:" << successCount << "/" << totalPoints << "，耗时:" << elapsed << "ms";
                }
            }
            countMutex.unlock();

            // 通知一个任务完成
            semaphore.release(1); });
    }

    // 等待所有加载任务完成，设置超时时间为5分钟
    bool allCompleted = true;
    for (int i = 0; i < totalPoints; ++i)
    {
        if (!semaphore.tryAcquire(1, 300000))
        { // 5分钟超时
            qDebug() << "等待任务完成超时！当前完成数量:" << data.readMassCount << "总数:" << totalPoints;
            allCompleted = false;
            break;
        }
        else
        {
            // qDebug() << "许可获取成功" << i << QDateTime::currentDateTime().toString("HH:MM:ss:zzzz");
        }
    }

    // 计算最终执行时间
    qint64 totalElapsed = timer.elapsed();
    qDebug() << "loadXic总执行时间:" << totalElapsed << "毫秒" << "是否全部完成:" << allCompleted;
    bool res = allCompleted && (successCount == totalPoints);
    return res;
}

QString DataReader::getDatFilePath(const QString &paramFileName)
{
    // 根据Param文件路径获取对应的Dat文件路径
    QString datFileName = paramFileName;

    if (datFileName.indexOf(".Param") != -1)
    {
        datFileName = datFileName.replace(".Param", ".Dat");
    }
    else if (datFileName.indexOf(".P") != -1)
    {
        datFileName = datFileName.replace(".P", ".D");
    }

    return datFileName;
}

QString DataReader::getTypeName(cParamValue::Type_Event type)
{
    switch (type)
    {
    case cParamValue::Type_Event_Null:
        return "Type_Event_Null";
        break;
    case cParamValue::Type_LIT2019:
        return "Type_LIT2019";

        break;
    case cParamValue::Type_LIT:
        return "Type_LIT";

        break;
    case cParamValue::Type_Profile:
        return "Type_Profile";

        break;
    case cParamValue::Type_Scan:
        return "Type_Scan";

        break;
    case cParamValue::Type_SIM:
        return "Type_SIM";

        break;
    case cParamValue::Type_Scan_RCT:
        return "Type_Scan_RCT";

        break;
    case cParamValue::Type_Static_RCT:
        return "Type_Static_RCT";

        break;
    case cParamValue::Type_MassBar:
        return "Type_MassBar";

        break;
    case cParamValue::Type_Scan_RGA:
        return "Type_Scan_RGA";

        break;
    case cParamValue::Type_VacuumDiagnostics_RGA:
        return "Type_VacuumDiagnostics_RGA";

        break;
    case cParamValue::Type_LeakCheck_RGA:
        return "Type_LeakCheck_RGA";

        break;
    case cParamValue::Type_SIM_RGA:
        return "Type_SIM_RGA";

        break;
    case cParamValue::Type_MRM:
        return "Type_MRM";

        break;
    case cParamValue::Type_SIM_2048:
        return "Type_SIM_2048";

        break;
    case cParamValue::Type_MRM_2048:
        return "Type_MRM_2048";

        break;
    }
}
int DataReader::splitStreamHead(QByteArray &segment, QByteArray &pStreamHead, QString &pPropertyStr,
                                QByteArray &pTuneFile) //, cParamValue::_Segment*& pSegment
{
    _StreamHead *p_StreamHead = (_StreamHead *)(pStreamHead.data());
    QDateTime mStartTime = QDateTime::fromTime_t(p_StreamHead->dateTime);
    QList<cParamValue::_StreamHeadParam *> tmpList;

    _CONGIG_OMS::_PARAM_FIT mCALIBRATE;

    if (!_StreamHead::toList(pStreamHead, tmpList))
        return -1;
    // mSegment.clear();
    QString mChartHead;
    QString mStrSelectXIC;
    for (int i = 0; i < tmpList.size(); ++i)
    {
        switch (tmpList[i]->type)
        {
        case cParamValue::Type_Segment_Param:
        {
            segment.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(segment.data(), tmpList[i]->param, segment.size());
            // pSegment= (cParamValue::_Segment*)(mSegment.data());
            break;
        }
        case cParamValue::Type_Method_Param:
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            mChartHead = QString::fromUtf8(tmpArray);
            QStringList tmpStringList = QString::fromUtf8(tmpArray).split("\n");
            if (tmpStringList.size() > 1)
            {
                for (int j = 0; j < tmpStringList.size(); ++j)
                {
                    if ("PolyFit" == tmpStringList[j])
                    {
                        _FUNTION_OMS::splitCalibrat(tmpStringList[j + 1], mCALIBRATE);
                        break;
                    }
                }
            }
            break;
        }
        case cParamValue::Type_Process_Param:
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            QStringList tmpStringList = QString::fromUtf8(tmpArray).split("\n");
            if (tmpStringList.size() > 1)
            {
                for (int j = 0; j < tmpStringList.size(); ++j)
                {
                    if ("Processing" == tmpStringList[j])
                    {
                        if (mDataProcess)
                            mDataProcess->setMethod(tmpStringList[j + 1]);
                        else
                            return 0;
                        break;
                    }
                }
            }
            break;
        }
        case cParamValue::Type_Property_Str:
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            pPropertyStr = QString::fromUtf8(tmpArray);
            //            pPropertyStr.resize(tmpList[i]->length- sizeof(cParamValue::_StreamHeadParam));
            //            memcpy(pPropertyStr.data(), tmpList[i]->param, pPropertyStr.size());
            break;
        }
        case cParamValue::Type_XIC_Param:
        {
            //            QByteArray tmpArray;
            //            tmpArray.resize(tmpList[i]->length-sizeof(cParamValue::_StreamHeadParam));
            //            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            //            mChartTIC->updateXIC(QString::fromUtf8(tmpArray));
            //            QMap<uint32_t, QMap<QString, _GRAPH_XIC*>>* pmap= mChartTIC->getXIC();

            //            foreach (uint32_t tmpKey, pmap->keys()) {
            //                QTreeWidgetItem* tempEventItem=new QTreeWidgetItem();
            //                tempEventItem->setText(0, tr("Event ")+ QString::number(tmpKey));
            //                tempEventItem->setCheckState(0,Qt::Checked);
            //                //tempTicItem->setData(0, Qt::UserRole, (int)(&(mChartTIC->getXIC()->value(tmpKey))));
            //                tempTicItem->addChild(tempEventItem);
            //                foreach (auto tmpKey1, pmap->value(tmpKey).keys()){
            //                    QTreeWidgetItem* tempItem=new QTreeWidgetItem();
            //                    tempItem->setTextColor(0, pmap->value(tmpKey)[tmpKey1]->color);
            //                    tempItem->setText(0, QString("m/z %1").arg(tmpKey1));
            //                    tempItem->setCheckState(0, Qt::Checked);
            //                    tempItem->setData(0, Qt::UserRole, (int)(pmap->value(tmpKey)[tmpKey1]->curve));
            //                    tempEventItem->addChild(tempItem);
            //                }
            //            }
            break;
        }
        case cParamValue::Type_Tuning_Param:
        {
            //            QFile file;
            //            if(path.isEmpty())
            //                file.setFileName(QCoreApplication::applicationDirPath()+"/tempFile.backup");
            //            else
            //                file.setFileName(path+"tempFile.backup");
            //            if(file.open(QIODevice::ReadWrite | QIODevice::Text)){
            //                file.write(tmpList[i]->param, tmpList[i]->length- sizeof(cParamValue::_StreamHeadParam));
            //                file.close();
            //            }
            int sizeFile = tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam);
            pTuneFile.resize(sizeFile);
            memcpy(pTuneFile.data(), tmpList[i]->param, sizeFile);
            break;
        }
        case cParamValue::Type_XIC_Select:
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            mStrSelectXIC = QString::fromUtf8(tmpArray);
            break;
        }
        default:
            break;
        }
    }
    return 1;
}

// 严格按照示例项目的dataDisassembleFirst函数实现
uint32_t DataReader::dataDisassembleFirst(QByteArray &pByteArray, cParamValue::_Segment *pSegment, QList<std::vector<double>> &pListX,
                                          QList<std::vector<double>> &pListY, QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> &pSTRUCT_DATA,
                                          bool restart)
{
    if (pByteArray.isEmpty() || (pSegment == nullptr))
        return 0;

    uint32_t uAllPoint = pByteArray.size() / sizeof(double);
    double dbEvtTimeSum = cParamValue::_Segment::getSegTimeMs(pSegment);
    if (dbEvtTimeSum < 0.0000001)
        return 0;

    if (static_cast<uint32_t>(pListY.size()) != pSegment->countsEvent)
    {
        pListY.clear();
        pListX.clear();
        for (uint32_t i = 0; i < pSegment->countsEvent; i++)
        {
            pListY.append(std::vector<double>(0));
            pListX.append(std::vector<double>(0));
        }
    }

    // 按照示例项目的方式处理restart参数
    if (restart)
    {
        pSTRUCT_DATA.clear();
        mPointTimeSIM.clear();
        for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; currentEvt++)
        {
            pSTRUCT_DATA.append(QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>(cParamValue::Type_Event_Null, _CONGIG_OMS::_STRUCT_DATA()));
            mPointTimeSIM.append(std::vector<quint32>());
        }
    }

    int offsetP = 0;
    // uint32_t uAllPoint = pByteArray.size() / sizeof(double);
    double *pdbOffset = (double *)(pByteArray.data());
    for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; ++currentEvt)
    {
        // pEventLIT=((cParamValue::_EventLIT*)((int*)&(pSegment->fisrtEvent)+ sizeof(cParamValue::_EventLIT)/sizeof(int)*currentEvt));
        cParamValue::_Event *pEvent = (cParamValue::_Event *)((char *)&(pSegment->fisrtEvent) + offsetP);

        // qDebug() << "事件类型:" << getTypeName(pEvent->type);
        if (pEvent->holdTimeMs < 0.0000001)
            continue;
#ifndef SIM
        if (cParamValue::Type_SIM == pEvent->type)
        {
            pSTRUCT_DATA[currentEvt].first = pEvent->type;
            offsetP += sizeof(cParamValue::_EventSIM);
            cParamValue::_EventSIM *pEventSIM = (cParamValue::_EventSIM *)pEvent;
            _CONGIG_OMS::_STRUCT_DATA *tempSTRUCT_DATA = &(pSTRUCT_DATA[currentEvt].second);
            // if(restart){
            tempSTRUCT_DATA->uEvtValidPoint = (uint32_t)(pEventSIM->holdTimeMs * uAllPoint / dbEvtTimeSum);
            if ((!vectorOperate::Resize(pListY[currentEvt], tempSTRUCT_DATA->uEvtValidPoint)) ||
                (!vectorOperate::Resize(pListX[currentEvt], tempSTRUCT_DATA->uEvtValidPoint)))
            {
                // mGraphBuffMutex.unlock();
                return 0;
            }
            _FUNTION_OMS::calibrationF(mCALIBRATE, pdbOffset, pEventSIM, pListX[currentEvt], pListY[currentEvt], mPointTimeSIM[currentEvt], tempSTRUCT_DATA);
            //}
            const double *pFirst = pdbOffset + tempSTRUCT_DATA->uDelayPoint;
            for (int indexM = 0; indexM < mPointTimeSIM[currentEvt].size(); ++indexM)
            {
                pFirst += tempSTRUCT_DATA->uPrePoint;
                double tempI = *pFirst;
                for (int i = 0; i < mPointTimeSIM[currentEvt][indexM]; ++i)
                {
                    tempI = tempI * i / (i + 1) + *pFirst / (i + 1);
                    ++pFirst;
                }
                pListY[currentEvt][indexM] = tempI;
            }
            pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;
        }
        else if (cParamValue::Type_Scan_RGA == pEvent->type)
        {
            pSTRUCT_DATA[currentEvt].first = pEvent->type;
            offsetP += sizeof(cParamValue::_EventScanRGA);
            cParamValue::_EventScanRGA *pEventScanRGA = (cParamValue::_EventScanRGA *)pEvent;
            _CONGIG_OMS::_STRUCT_DATA *tempSTRUCT_DATA =
                &(pSTRUCT_DATA[currentEvt].second); //_CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt]);
            // if(restart){
            tempSTRUCT_DATA->uEvtValidPoint = uAllPoint;
            if ((!vectorOperate::Resize(pListY[currentEvt], tempSTRUCT_DATA->uEvtValidPoint)) ||
                (!vectorOperate::Resize(pListX[currentEvt], tempSTRUCT_DATA->uEvtValidPoint)))
            {
                // mGraphBuffMutex.unlock();
                return 0;
            }
            tempSTRUCT_DATA->uDelayPoint = 0;
            tempSTRUCT_DATA->uPrePoint = 0;
            tempSTRUCT_DATA->uPostPoint = 0;
            tempSTRUCT_DATA->uEventPoint = tempSTRUCT_DATA->uEvtValidPoint;
            int sizeBuffer = (pEventScanRGA->mass_end - pEventScanRGA->mass_begin) * pEventScanRGA->points_per_amu + 1;
            double stepMass = 1.0 / static_cast<double>(pEventScanRGA->points_per_amu);
            for (int i = 0; i < sizeBuffer; ++i)
            {
                pListX[currentEvt][i] = pEventScanRGA->mass_begin + stepMass * i;
            }

            //                _FUNTION_OMS::calibrationF(mCALIBRATE,
            //                                           (*pListX)[currentEvt],
            //                                           pEventLIT,
            //                                           tempSTRUCT_DATA);
            //}
            memcpy(pListY[currentEvt].data(), pdbOffset, tempSTRUCT_DATA->uEvtValidPoint * sizeof(double));
            pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;
        }
        else
        { // Type_LIT, Type_Profile, Type_Scan
#endif
          //            _CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt]); \
    //            tempSTRUCT_DATA->uEvtValidPoint = (uint32_t)(pEventLIT->holdTime * uAllPoint / dbEvtTimeSum); \
    //            if((!vectorOperate::Resize((*pListY)[currentEvt],tempSTRUCT_DATA->uEvtValidPoint)) \
    //                    ||(!vectorOperate::Resize((*pListX)[currentEvt],tempSTRUCT_DATA->uEvtValidPoint))) \
    //                return 0; \
    //            if(restart){ \
    //                _CONGIG_OMS::calibrationF(mCALIBRATE, (*pListX)[currentEvt], pEventLIT,
          //                tempSTRUCT_DATA);//_CONGIG_OMS::calibrationF(mCALIBRATE, \
    //                pFirst, pLast, pEventLIT, tempSTRUCT_DATA); tempSTRUCT_DATA->uEventPoint= tempSTRUCT_DATA->uEvtValidPoint- \
    //                tempSTRUCT_DATA->uPrePoint- tempSTRUCT_DATA->uPostPoint; \
    //             } \
    //            memcpy((*pListY)[currentEvt].data(), pdbOffset, tempSTRUCT_DATA->uEvtValidPoint* sizeof(double)); \
    //            pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;

            pSTRUCT_DATA[currentEvt].first = pEvent->type;
            offsetP += sizeof(cParamValue::_EventLIT);
            cParamValue::_EventLIT *pEventLIT = (cParamValue::_EventLIT *)pEvent;
            _CONGIG_OMS::_STRUCT_DATA *tempSTRUCT_DATA =
                &(pSTRUCT_DATA[currentEvt].second); //_CONGIG_OMS::_STRUCT_DATA* tempSTRUCT_DATA= &(pSTRUCT_DATA[currentEvt]);
            // if(restart){
            tempSTRUCT_DATA->uEvtValidPoint = (uint32_t)(pEventLIT->holdTimeMs * uAllPoint / dbEvtTimeSum);
            if ((!vectorOperate::Resize(pListY[currentEvt], tempSTRUCT_DATA->uEvtValidPoint)) ||
                (!vectorOperate::Resize(pListX[currentEvt], tempSTRUCT_DATA->uEvtValidPoint)))
            {
                // mGraphBuffMutex.unlock();
                return 0;
            }
            _FUNTION_OMS::calibrationF(mCALIBRATE, pListX[currentEvt], pEventLIT,
                                       tempSTRUCT_DATA); //_CONGIG_OMS::calibrationF(mCALIBRATE, pFirst, pLast, pEventLIT, tempSTRUCT_DATA);
            // tempSTRUCT_DATA->uEventPoint= tempSTRUCT_DATA->uEvtValidPoint- tempSTRUCT_DATA->uPrePoint- tempSTRUCT_DATA->uPostPoint;
            //}
            memcpy(pListY[currentEvt].data(), pdbOffset, tempSTRUCT_DATA->uEvtValidPoint * sizeof(double));
            pdbOffset += tempSTRUCT_DATA->uEvtValidPoint;
        }
    }
    return uAllPoint;
}
// bool DataReader::dataAnalyze(QList<QPair<cParamValue::Type_Event, _CONGIG_OMS::_STRUCT_DATA>> &mListSTRUCT_DATA, int indexFile, uint32_t currentEvt,
//                              std::vector<double> &pThreadBuffX, std::vector<double> &pThreadBuffY, std::vector<double> &pGraphBuffX,
//                              std::vector<double> &pGraphBuffY, _STRUCT_PEAK &pSTRUCT_PEAK, FileData &data)
// {
//     uint32_t countEvt = mListSTRUCT_DATA.size();
//     _CONGIG_OMS::_STRUCT_DATA *pSTRUCT_DATA = &(mListSTRUCT_DATA[currentEvt].second);
//     if ((cParamValue::Type_SIM == mListSTRUCT_DATA[currentEvt].first) || (cParamValue::Type_SIM_2048 == mListSTRUCT_DATA[currentEvt].first)) {
//         qDebug() << "分析SIM";
//         if (!AnalyzeSIM(pThreadBuffX, pThreadBuffY, *pSTRUCT_DATA, currentEvt, countEvt, pSTRUCT_PEAK, data)) {
//             qDebug() << "E_MemoryOverflow";
//             // mGraphBuffMutexMass.unlock();
//             return 0;
//         }
//     } else {
//         qDebug() << "分析MASS_BAR_CHART";

//         // currentChart() == _CONGIG_OMS::MASS_BAR_CHART
//         if (1) {
//             if (!AnalyzeMassBar(pThreadBuffX.data(), pThreadBuffY.data(), *pSTRUCT_DATA, currentEvt, countEvt, pSTRUCT_PEAK, 0, data)) {
//                 qDebug() << "E_MemoryOverflow";
//                 // mGraphBuffMutexMass.unlock();
//                 return 0;
//             }
//         } else {
//             if (!AnalyzeScan(pThreadBuffX.data(), pThreadBuffY.data(), *pSTRUCT_DATA, currentEvt, countEvt, pSTRUCT_PEAK, 99, data)) {
//                 qDebug() << "E_MemoryOverflow";
//                 // mGraphBuffMutexMass.unlock();
//                 return 0;
//             }
//         }
//     }
// }
// bool DataReader::AnalyzeSIM(const std::vector<double> &pX, const std::vector<double> &pY, _CONGIG_OMS::_STRUCT_DATA &pSTRUCT_DATA, int currentEvt, int
// countEvt,
//                             _STRUCT_PEAK &pSTRUCT_PEAK, FileData &data)
// {
//     quint32 sizePeak = pX.size();
//     pSTRUCT_PEAK.resize(sizePeak);
//     for (int i = 0; i < sizePeak; ++i) {
//         pSTRUCT_PEAK.Absc[i] = pX[i];
//         pSTRUCT_PEAK.Ord[i] = pY[i];
//     }
//     //    _STRUCT_PEAK tempSTRUCT_PEAK;
//     //    tempSTRUCT_PEAK.Absc.resize(sizePeak);
//     //    tempSTRUCT_PEAK.Ord.resize(sizePeak);

//     // vector<double> tempX(pSTRUCT_DATA.uEventPoint);
//     // vector<double> tempY(pSTRUCT_DATA.uEventPoint);

//     QVector<QString> strMarker(sizePeak);

//     for (uint32_t jj = 0; jj < sizePeak; jj++)
//         strMarker[jj] = QString::number(pX[jj]) + "," + QString::number(pY[jj]);

//     //    mStrongPeakMutex.lock();
//     //    while(mListStrongPeak.size()> countEvt) mListStrongPeak.removeLast();
//     //    while(mListStrongPeak.size()< countEvt) mListStrongPeak.append(_STRUCT_GRAPH(0));
//     //    vector<double> tempX(pSTRUCT_DATA.uEventPoint);
//     //    vector<double> tempY(pSTRUCT_DATA.uEventPoint);
//     //    memcpy(tempX.data(), pX + pSTRUCT_DATA.uDelayPoint + pSTRUCT_DATA.uPrePoint, pSTRUCT_DATA.uEventPoint * sizeof(double));
//     //    memcpy(tempY.data(), pY + pSTRUCT_DATA.uDelayPoint + pSTRUCT_DATA.uPrePoint, pSTRUCT_DATA.uEventPoint * sizeof(double));

//     mGraphBuffMutex.lock();

//     QList<std::vector<double>> &mAbsc = data.mAbsc;
//     QList<std::vector<double>> &mOrd = data.mOrd;
//     QList<_STRUCT_GRAPH> &mList_PEAK = data.mList_PEAK;

//     // while(mGraphBuffX->size()> countEvt) mGraphBuffX->removeLast();
//     // while(mGraphBuffY->size()> countEvt) mGraphBuffY->removeLast();
//     while (mAbsc.size() > countEvt)
//         mAbsc.removeLast();
//     while (mOrd.size() > countEvt)
//         mOrd.removeLast();
//     while (mList_PEAK.size() > countEvt)
//         mList_PEAK.removeLast();
//     // while(mGraphBuffX->size()< countEvt) mGraphBuffX->append(std::vector<double>(0));
//     // while(mGraphBuffY->size()< countEvt) mGraphBuffY->append(std::vector<double>(0));
//     while (mAbsc.size() < countEvt)
//         mAbsc.append(std::vector<double>(0));
//     while (mOrd.size() < countEvt)
//         mOrd.append(std::vector<double>(0));
//     while (mList_PEAK.size() < countEvt)
//         mList_PEAK.append(_STRUCT_GRAPH(0));
//     if (!(vectorOperate::Resize(mAbsc[currentEvt], sizePeak + 1) && vectorOperate::Resize(mOrd[currentEvt], sizePeak + 1))) {
//         mGraphBuffMutex.unlock();
//         return false;
//     }
//     mList_PEAK[currentEvt].Marker.resize(sizePeak);
//     if (!(vectorOperate::Resize(mList_PEAK[currentEvt].Absc, sizePeak) && vectorOperate::Resize(mList_PEAK[currentEvt].Ord, sizePeak))) {
//         mGraphBuffMutex.unlock();
//         return false;
//     }
//     // std::copy(tempX.begin(),tempX.end(),(*mGraphBuffX)[currentEvt].begin());//std::copy(tempX.begin(),tempX.end(),mGraphBuff[0]->begin()+nOffsetBuff);
//     // std::copy(tempY.begin(),tempY.end(),(*mGraphBuffY)[currentEvt].begin());//std::copy(tempY.begin(),tempY.end(),mGraphBuff[1]->begin()+nOffsetBuff);
//     if (sizePeak > 1) {
//         //        if(!(vectorOperate::Resize((*mAbsc)[currentEvt] ,sizePeak+1)
//         //             &&vectorOperate::Resize((*mOrd)[currentEvt], sizePeak+1))){
//         //            mGraphBuffMutex.unlock();
//         //            return false;
//         //        }
//         std::copy(pSTRUCT_PEAK.Absc.begin(), pSTRUCT_PEAK.Absc.end(), mAbsc[currentEvt].begin());
//         std::copy(pSTRUCT_PEAK.Ord.begin(), pSTRUCT_PEAK.Ord.end(), mOrd[currentEvt].begin());
//         // mMinLine= tempY[0];
//         for (uint i = 1; i < mOrd[currentEvt].size(); ++i)
//             mList_PEAK[currentEvt].baseLine = min(mList_PEAK[currentEvt].baseLine, mOrd[currentEvt][i]);
//         mAbsc[currentEvt][sizePeak] = pSTRUCT_PEAK.Absc[sizePeak - 1];
//         mAbsc[currentEvt][sizePeak - 1] = (pSTRUCT_PEAK.Absc[sizePeak - 2] + pSTRUCT_PEAK.Absc[sizePeak - 1]) / 2;
//         mOrd[currentEvt][sizePeak] = pSTRUCT_PEAK.Ord[sizePeak - 1];
//         mOrd[currentEvt][sizePeak - 1] = mList_PEAK[currentEvt].baseLine;
//         std::copy(pSTRUCT_PEAK.Absc.begin(), pSTRUCT_PEAK.Absc.end(), mList_PEAK[currentEvt].Absc.begin());
//         std::copy(pSTRUCT_PEAK.Ord.begin(), pSTRUCT_PEAK.Ord.end(), mList_PEAK[currentEvt].Ord.begin());
//         std::copy(strMarker.begin(), strMarker.end(), mList_PEAK[currentEvt].Marker.begin());
//         /*if(mSearchResult){
//             mSearchResult->setSearchData((*mList_PEAK)[currentEvt].Absc, (*mList_PEAK)[currentEvt].Ord);
//         }
//         if(mBuildDB){
//             mBuildDB->setBuildLibData((*mList_PEAK)[currentEvt].Absc, (*mList_PEAK)[currentEvt].Ord);
//         }*/
//     } else if (1 == sizePeak) {
//         //        if(!(vectorOperate::Resize((*mAbsc)[currentEvt] ,sizePeak+2)
//         //             &&vectorOperate::Resize((*mOrd)[currentEvt], sizePeak+2))){
//         //            mGraphBuffMutex.unlock();
//         //            return false;
//         //        }
//         // std::copy(pSTRUCT_PEAK.Absc.begin(),pSTRUCT_PEAK.Absc.end(),(*mAbsc)[currentEvt].begin());
//         // std::copy(pSTRUCT_PEAK.Ord.begin(),pSTRUCT_PEAK.Ord.end(),(*mOrd)[currentEvt].begin());
//         // mMinLine= tempY[0];
//         //        for(uint i=1; i< (*mOrd)[currentEvt].size(); ++i)
//         //            (*mList_PEAK)[currentEvt].baseLine= min((*mList_PEAK)[currentEvt].baseLine, (*mOrd)[currentEvt][i]);
//         mAbsc[currentEvt][sizePeak] = pSTRUCT_PEAK.Absc[sizePeak - 1];
//         mAbsc[currentEvt][sizePeak - 1] = pSTRUCT_PEAK.Absc[sizePeak - 1] - 0.0000001; //(pSTRUCT_PEAK.Absc[sizePeak-2]+ pSTRUCT_PEAK.Absc[sizePeak-1])/2;
//         // mAbsc[currentEvt][sizePeak+1]= pSTRUCT_PEAK.Absc[sizePeak-1]+0.0000001;
//         mOrd[currentEvt][sizePeak] = pSTRUCT_PEAK.Ord[sizePeak - 1];
//         mOrd[currentEvt][sizePeak - 1] = mList_PEAK[currentEvt].baseLine;
//         // mOrd[currentEvt][sizePeak+1]= mList_PEAK[currentEvt].baseLine;
//         std::copy(pSTRUCT_PEAK.Absc.begin(), pSTRUCT_PEAK.Absc.end(), mList_PEAK[currentEvt].Absc.begin());
//         std::copy(pSTRUCT_PEAK.Ord.begin(), pSTRUCT_PEAK.Ord.end(), mList_PEAK[currentEvt].Ord.begin());
//         std::copy(strMarker.begin(), strMarker.end(), mList_PEAK[currentEvt].Marker.begin());
//         /*if(mSearchResult){
//             mSearchResult->setSearchData(mList_PEAK[currentEvt].Absc, mList_PEAK[currentEvt].Ord);
//         }
//         if(mBuildDB){
//             mBuildDB->setBuildLibData(mList_PEAK[currentEvt].Absc, mList_PEAK[currentEvt].Ord);
//         }*/
//     }
//     mGraphBuffMutex.unlock();
//     return true;
// }
// bool DataReader::AnalyzeMassBar(double *pX, double *pY, _CONGIG_OMS::_STRUCT_DATA &pSTRUCT_DATA, int currentEvt, int countEvt, _STRUCT_PEAK &pSTRUCT_PEAK,
//                                 double baseX, FileData &data)
// {
//     quint32 sizeData = pSTRUCT_DATA.uEventPoint;
//     if (sizeData <= 0)
//         return false;
//     //    _STRUCT_PEAK tempSTRUCT_PEAK;
//     //    vector<int> tmpMarker(pSTRUCT_DATA.uEventPoint);
//     //    for(uint32_t ii=0; ii<pSTRUCT_DATA.uEventPoint; ii++)
//     //        tmpMarker[ii]=ii;
//     vector<double> tempX(sizeData);
//     vector<double> tempY(sizeData);
//     memcpy(tempX.data(), pX + pSTRUCT_DATA.uDelayPoint + pSTRUCT_DATA.uPrePoint, sizeData * sizeof(double));
//     memcpy(tempY.data(), pY + pSTRUCT_DATA.uDelayPoint + pSTRUCT_DATA.uPrePoint, sizeData * sizeof(double));

//     quint32 sizePeak = (int)(tempX[sizeData - 1] + 0.5 - tempX[0]) + 1;
//     pSTRUCT_PEAK.resize(sizePeak);
//     for (int i = 0; i < sizePeak; ++i) {
//         pSTRUCT_PEAK.Absc[i] = (int)tempX[0] + i;
//         pSTRUCT_PEAK.Ord[i] = 0;
//     }
//     quint32 indexData = 0;
//     double startMass = 0, endMass = 0;
//     for (quint32 i = 0; i < sizePeak; ++i) {
//         startMass = pSTRUCT_PEAK.Absc[i] - 0.3;
//         endMass = pSTRUCT_PEAK.Absc[i] + 0.3;
//         while (indexData < sizeData) {
//             if (tempX[indexData] < startMass)
//                 ++indexData;
//             else
//                 break;
//         }
//         // quint32 countY=1;
//         while (indexData < sizeData) {
//             if (tempX[indexData] < endMass) {
//                 if (tempY[indexData] > pSTRUCT_PEAK.Ord[i])
//                     pSTRUCT_PEAK.Ord[i] = tempY[indexData]; // pSTRUCT_PEAK.Ord[i]/(countY)*(countY-1)+ tempY[indexData]/(countY);
//                 ++indexData;
//                 //++countY;
//             } else
//                 break;
//         }
//     }
//     quint32 indexTempAbsc = 0;

//     auto biggest = std::max_element(pSTRUCT_PEAK.Ord.begin(), pSTRUCT_PEAK.Ord.end());
//     if (*biggest < baseX + 0.00001) {
//         std::copy(pSTRUCT_PEAK.Absc.begin(), pSTRUCT_PEAK.Absc.end(), tempX.begin());
//         std::copy(pSTRUCT_PEAK.Ord.begin(), pSTRUCT_PEAK.Ord.end(), tempY.begin());
//     } else {
//         for (uint32_t jj = 0; jj < sizePeak; ++jj) {
//             if (pSTRUCT_PEAK.Ord[jj] > baseX + 0.000001) {
//                 tempX[indexTempAbsc] = pSTRUCT_PEAK.Absc[jj];
//                 tempY[indexTempAbsc] = pSTRUCT_PEAK.Ord[jj];
//                 ++indexTempAbsc;
//             }
//         }
//     }

//     //    sizePeak= indexTempAbsc;
//     sizePeak = indexTempAbsc > 0 ? indexTempAbsc : sizePeak;

//     double yTIC = 0;
//     for (int i = 0; i < sizePeak; i++) {
//         yTIC += tempY[i];
//     }
//     // TIC值：若峰全在基线以下，=基线值累加；若有超过基线的，超过基线值累加。
//     //     double yTIC = accumulate(tempY.begin(), tempY.begin()+sizePeak, 0);
//     if (yTIC > 0) {
//         for (int i = 0; i < sizePeak; i++) {
//             tempY[i] = tempY[i] / yTIC; // * mSysVacuum;
//         }
//     }

//     QVector<QString> strMarker(sizePeak);
//     for (uint32_t jj = 0; jj < sizePeak; ++jj)
//         strMarker[jj] = QString::number(tempX[jj]);

//     mGraphBuffMutex.lock();

//     QList<std::vector<double>> &mAbsc = data.mAbsc;
//     QList<std::vector<double>> &mOrd = data.mOrd;
//     QList<_STRUCT_GRAPH> &mList_PEAK = data.mList_PEAK;

//     while (mAbsc.size() > countEvt)
//         mAbsc.removeLast();
//     while (mOrd.size() > countEvt)
//         mOrd.removeLast();
//     while (mList_PEAK.size() > countEvt)
//         mList_PEAK.removeLast();
//     while (mAbsc.size() < countEvt)
//         mAbsc.append(std::vector<double>(0));
//     while (mOrd.size() < countEvt)
//         mOrd.append(std::vector<double>(0));
//     while (mList_PEAK.size() < countEvt)
//         mList_PEAK.append(_STRUCT_GRAPH(0));
//     if (!(vectorOperate::Resize(mAbsc[currentEvt], sizePeak) && vectorOperate::Resize(mOrd[currentEvt], sizePeak))) {
//         mGraphBuffMutex.unlock();
//         return false;
//     }
//     mList_PEAK[currentEvt].Marker.resize(sizePeak);
//     if (!(vectorOperate::Resize(mList_PEAK[currentEvt].Absc, sizePeak) && vectorOperate::Resize(mList_PEAK[currentEvt].Ord, sizePeak))) {
//         mGraphBuffMutex.unlock();
//         return false;
//     }
//     mList_PEAK[currentEvt].baseLine = baseX;
//     // memcpy((*mAbsc)[currentEvt].data(), tempX.data(), sizePeak*sizeof(double));//
//     std::copy(tempX.begin(), tempX.begin() + sizePeak, mAbsc[currentEvt].begin());
//     // memcpy((*mOrd)[currentEvt].data(), tempY.data(), sizePeak*sizeof(double));//
//     std::copy(tempY.begin(), tempY.begin() + sizePeak, mOrd[currentEvt].begin());
//     // memcpy((*mList_PEAK)[currentEvt].Absc.data(), tempX.data(), sizePeak*sizeof(double));//
//     std::copy(tempX.begin(), tempX.begin() + sizePeak, mList_PEAK[currentEvt].Absc.begin());
//     // memcpy((*mList_PEAK)[currentEvt].Ord.data(), tempY.data(), sizePeak*sizeof(double));//
//     std::copy(tempY.begin(), tempY.begin() + sizePeak, mList_PEAK[currentEvt].Ord.begin());
//     // memcpy((*mList_PEAK)[currentEvt].Marker.data(), strMarker.data(), sizePeak*sizeof(double));//
//     std::copy(strMarker.begin(), strMarker.end(), mList_PEAK[currentEvt].Marker.begin());

//     /*if(mSearchResult){
//         mSearchResult->setSearchData((*mList_PEAK)[currentEvt].Absc, (*mList_PEAK)[currentEvt].Ord);
//     }
//     if(mBuildDB){
//         mBuildDB->setBuildLibData((*mList_PEAK)[currentEvt].Absc, (*mList_PEAK)[currentEvt].Ord);
//     }*/

//     mGraphBuffMutex.unlock();
//     return true;
// }
// bool DataReader::AnalyzeScan(double *pX, double *pY, _CONGIG_OMS::_STRUCT_DATA &pSTRUCT_DATA, int currentEvt, int countEvt, _STRUCT_PEAK &pSTRUCT_PEAK,
//                              uint32_t uWidth, FileData &data)
// {
//     if (pSTRUCT_DATA.uEventPoint <= 0)
//         return false;
//     _STRUCT_PEAK tempSTRUCT_PEAK;
//     vector<int> tmpMarker(pSTRUCT_DATA.uEventPoint);
//     for (uint32_t ii = 0; ii < pSTRUCT_DATA.uEventPoint; ii++)
//         tmpMarker[ii] = ii;
//     vector<double> tempX(pSTRUCT_DATA.uEventPoint);
//     vector<double> tempY(pSTRUCT_DATA.uEventPoint);
//     memcpy(tempX.data(), pX + pSTRUCT_DATA.uDelayPoint + pSTRUCT_DATA.uPrePoint, pSTRUCT_DATA.uEventPoint * sizeof(double));
//     memcpy(tempY.data(), pY + pSTRUCT_DATA.uDelayPoint + pSTRUCT_DATA.uPrePoint, pSTRUCT_DATA.uEventPoint * sizeof(double));

//     if (mSignalProcessing.CentroidData(tempX, tempY, pSTRUCT_PEAK.Absc, pSTRUCT_PEAK.Ord, pSTRUCT_PEAK.Start, pSTRUCT_PEAK.End, pSTRUCT_PEAK.Area, 6) ==
//         -1) // 找峰
//         return false;
//     quint32 sizePeak = pSTRUCT_PEAK.Absc.size();
//     if (uWidth > sizePeak) // if(uWidth> pSTRUCT_PEAK.Absc.size())
//         uWidth = sizePeak;
//     //    mBaseLine.resize(uEvtValidPoint);
//     //    std::copy(pY.begin(),pY.end(),mBaseLine.begin());
//     //    step.resize(mStart.size());
//     //    for(uint32_t i=0;i<step.size();i++){
//     //        step[i]=(mBaseLine[iEnd[i]]-mBaseLine[iStart[i]])/(iEnd[i]-iStart[i]);
//     //        for(int j=0;j<iEnd[i]-iStart[i];j++)
//     //            mBaseLine[iStart[i]+j]=step[i]*j+mBaseLine[iStart[i]];
//     //    }
//     //    for(uint32_t i=0;i<uEvtValidPoint;i++)
//     //        mBaseLine[i]=pY[i]-mBaseLine[i];
//     //    double dNumerator=0,dDenominator=0;
//     //    for(uint32_t i=0;i<iStart.size();i++){
//     //        dNumerator=0;
//     //        dDenominator=0;
//     //        for(int j=iStart[i];j<=iEnd[i];j++){
//     //            dNumerator += mDataBuff[0][j] * mBaseLine[j];
//     //            dDenominator += mBaseLine[j];
//     //        }
//     //        if(dDenominator>0.0000001||dDenominator<-0.0000001)
//     //            *mAbsc[i] = dNumerator / dDenominator;
//     //    }
//     cDataSort::DateSort(pSTRUCT_PEAK.Absc, pSTRUCT_PEAK.Ord, pSTRUCT_PEAK.Area, pSTRUCT_PEAK.Start, pSTRUCT_PEAK.End, tempSTRUCT_PEAK.Absc,
//     tempSTRUCT_PEAK.Ord,
//                         tempSTRUCT_PEAK.Area, tempSTRUCT_PEAK.Start, tempSTRUCT_PEAK.End, tmpMarker, uWidth);
//     QVector<QString> strMarker(uWidth);

//     vector<double> cHHPW = mSignalProcessing.GetHHPW(tempX, tempY, tempSTRUCT_PEAK.Start, tempSTRUCT_PEAK.End); // 计算半高峰宽
//     if (cHHPW.size() >= uWidth) {
//         for (uint32_t jj = 0; jj < uWidth; jj++)
//             strMarker[jj] = QString::number(cHHPW[jj]) + "\n" + // QString::number(tempSTRUCT_PEAK.Absc[jj])+","+QString::number(tempSTRUCT_PEAK.Ord[jj]);
//                             QString::number(tempSTRUCT_PEAK.Absc[jj]) + "," + QString::number(tempSTRUCT_PEAK.Area[jj]);
//     }
//     mGraphBuffMutex.lock();

//     QList<QList<std::vector<double>>> &mGraphBuffX = data.mGraphBuffX; // std::vector<double>* mGraphBuff[2];         //size-allPoint
//     QList<QList<std::vector<double>>> &mGraphBuffY = data.mGraphBuffY;
//     QList<std::vector<double>> &mAbsc = data.mAbsc;
//     QList<std::vector<double>> &mOrd = data.mOrd;
//     QList<_STRUCT_GRAPH> &mList_PEAK = data.mList_PEAK;
//     while (mGraphBuffX[0].size() > countEvt)
//         mGraphBuffX[0].removeLast();
//     while (mGraphBuffY[0].size() > countEvt)
//         mGraphBuffY[0].removeLast();
//     while (mAbsc.size() > countEvt)
//         mAbsc.removeLast();
//     while (mOrd.size() > countEvt)
//         mOrd.removeLast();
//     while (mList_PEAK.size() > countEvt)
//         mList_PEAK.removeLast();
//     while (mGraphBuffX[0].size() < countEvt)
//         mGraphBuffX[0].append(std::vector<double>(0));
//     while (mGraphBuffY[0].size() < countEvt)
//         mGraphBuffY[0].append(std::vector<double>(0));
//     while (mAbsc.size() < countEvt)
//         mAbsc.append(std::vector<double>(0));
//     while (mOrd.size() < countEvt)
//         mOrd.append(std::vector<double>(0));
//     while (mList_PEAK.size() < countEvt)
//         mList_PEAK.append(_STRUCT_GRAPH(0));
//     if (!(vectorOperate::Resize(mGraphBuffX[0][currentEvt], pSTRUCT_DATA.uEventPoint) &&
//           vectorOperate::Resize(mGraphBuffY[0][currentEvt], pSTRUCT_DATA.uEventPoint) && vectorOperate::Resize(mAbsc[currentEvt], sizePeak + 1) &&
//           vectorOperate::Resize(mOrd[currentEvt], sizePeak + 1))) {
//         mGraphBuffMutex.unlock();
//         return false;
//     }
//     mList_PEAK[currentEvt].Marker.resize(uWidth);
//     if (!(vectorOperate::Resize(mList_PEAK[currentEvt].Absc, uWidth) && vectorOperate::Resize(mList_PEAK[currentEvt].Ord, uWidth))) {
//         mGraphBuffMutex.unlock();
//         return false;
//     }
//     std::copy(tempX.begin(), tempX.end(), mGraphBuffX[0][currentEvt].begin()); // std::copy(tempX.begin(),tempX.end(),mGraphBuff[0]->begin()+nOffsetBuff);
//     std::copy(tempY.begin(), tempY.end(), mGraphBuffY[0][currentEvt].begin()); // std::copy(tempY.begin(),tempY.end(),mGraphBuff[1]->begin()+nOffsetBuff);
//     if (uWidth > 1) {
//         std::copy(pSTRUCT_PEAK.Absc.begin(), pSTRUCT_PEAK.Absc.end(), mAbsc[currentEvt].begin());
//         std::copy(pSTRUCT_PEAK.Ord.begin(), pSTRUCT_PEAK.Ord.end(), mOrd[currentEvt].begin());
//         mList_PEAK[currentEvt].baseLine = tempY[0];
//         for (int i = 1; i < pSTRUCT_DATA.uEventPoint; ++i)
//             mList_PEAK[currentEvt].baseLine = min(mList_PEAK[currentEvt].baseLine, tempY[i]);
//         mAbsc[currentEvt][sizePeak] = pSTRUCT_PEAK.Absc[sizePeak - 1];
//         mAbsc[currentEvt][sizePeak - 1] = (pSTRUCT_PEAK.Absc[sizePeak - 2] + pSTRUCT_PEAK.Absc[sizePeak - 1]) / 2;
//         mOrd[currentEvt][sizePeak] = pSTRUCT_PEAK.Ord[sizePeak - 1];
//         mOrd[currentEvt][sizePeak - 1] = mList_PEAK[currentEvt].baseLine;
//         std::copy(tempSTRUCT_PEAK.Absc.begin(), tempSTRUCT_PEAK.Absc.end(), mList_PEAK[currentEvt].Absc.begin());
//         std::copy(tempSTRUCT_PEAK.Ord.begin(), tempSTRUCT_PEAK.Ord.end(), mList_PEAK[currentEvt].Ord.begin());
//         std::copy(strMarker.begin(), strMarker.end(), mList_PEAK[currentEvt].Marker.begin());

//         /*if(mBuildDB){
//             mBuildDB->setBuildLibData((*mList_PEAK)[currentEvt].Absc, (*mList_PEAK)[currentEvt].Ord);
//         }
//         if(mSearchResult){
//             mSearchResult->setSearchData((*mList_PEAK)[currentEvt].Absc, (*mList_PEAK)[currentEvt].Ord);
//         }*/
//     }
//     mGraphBuffMutex.unlock();
//     return true;
// }
