#include <QCoreApplication>
#include <QDebug>
#include <QFileInfo>
#include <QElapsedTimer>
#include "LxDataReader/lxcustomdatareader.h"
#include "FileData/filedata.h"

/**
 * @brief 简单测试新的数据读取器
 * 
 * 这个程序用于快速验证LxCustomDataReader是否能正常工作
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 简单数据读取器测试 ===";
    
    // 测试文件路径
    QString testFilePath = "C:/Users/<USER>/Desktop/新版数据格式20250717/20250724-抗生素-C1.Param";
    
    // 检查文件是否存在
    QFileInfo fileInfo(testFilePath);
    if (!fileInfo.exists()) {
        qDebug() << "❌ 测试文件不存在:" << testFilePath;
        qDebug() << "请确保文件路径正确";
        return -1;
    }
    
    qDebug() << "📁 测试文件:" << testFilePath;
    qDebug() << "📊 文件大小:" << QString::number(fileInfo.size() / 1024.0 / 1024.0, 'f', 2) << "MB";
    
    try {
        // 创建自定义数据读取器
        LxCustomDataReader *customReader = new LxCustomDataReader();
        qDebug() << "✅ 自定义读取器创建成功";
        
        // 创建FileData对象
        FileData *testData = new FileData(testFilePath);
        qDebug() << "✅ FileData对象创建成功";
        
        qDebug() << "\n=== 开始读取数据 ===";
        
        QElapsedTimer timer;
        timer.start();
        
        // 测试TIC数据读取
        bool success = customReader->loadTICDataComplete(testData);
        
        qint64 elapsedTime = timer.elapsed();
        
        if (success) {
            qDebug() << "🎉 数据读取成功！";
            qDebug() << "⏱️ 读取耗时:" << elapsedTime << "毫秒";
            
            // 检查基本数据
            QList<int> eventIds = testData->getAllEventIds();
            qDebug() << "📊 事件数量:" << eventIds.size();
            
            int totalDataPoints = 0;
            double minTime = std::numeric_limits<double>::max();
            double maxTime = std::numeric_limits<double>::min();
            
            for (int eventId : eventIds) {
                TicChartData *ticData = testData->getTicData(eventId);
                if (ticData) {
                    QVector<QPointF> ticPoints = ticData->getDataThreadSafe();
                    totalDataPoints += ticPoints.size();
                    
                    if (!ticPoints.isEmpty()) {
                        minTime = qMin(minTime, ticPoints.first().x());
                        maxTime = qMax(maxTime, ticPoints.last().x());
                    }
                    
                    qDebug() << "📈 事件" << eventId << "数据点数:" << ticPoints.size();
                }
            }
            
            if (totalDataPoints > 0) {
                qDebug() << "📊 总数据点数:" << totalDataPoints;
                qDebug() << "⏰ 时间范围:" << QString::number(minTime, 'f', 2) << "~" << QString::number(maxTime, 'f', 2) << "秒";
                double timeSpan = maxTime - minTime;
                qDebug() << "⏰ 时间跨度:" << QString::number(timeSpan, 'f', 2) << "秒 (" << QString::number(timeSpan/3600.0, 'f', 2) << "小时)";
                
                // 检查是否解决了数据截断问题
                if (timeSpan > 86400) {  // 超过24小时
                    qDebug() << "🎉 成功读取超过24小时的数据，Period限制问题已解决！";
                } else {
                    qDebug() << "ℹ️ 数据时间跨度在24小时内，无法确定是否解决Period限制问题";
                }
            }
            
            // 检查索引数组
            const std::vector<qint64> &indexArray = testData->getIndexArray();
            qDebug() << "🗂️ 索引数组大小:" << indexArray.size();
            
        } else {
            qDebug() << "❌ 数据读取失败！";
            qDebug() << "⏱️ 失败耗时:" << elapsedTime << "毫秒";
        }
        
        // 清理资源
        delete testData;
        delete customReader;
        
        qDebug() << "\n=== 测试完成 ===";
        return success ? 0 : -1;
        
    } catch (const std::exception &e) {
        qDebug() << "❌ 程序异常:" << e.what();
        return -1;
    } catch (...) {
        qDebug() << "❌ 未知异常";
        return -1;
    }
}
