#ifndef LXCHARTDATA_H
#define LXCHARTDATA_H

#include <QObject>
#include <QVector>
#include <QPointF>
#include <QString>
#include <QPair>
#include <QColor>
#include <QMutex>
#include "Globals/GlobalDefine.h"
#include <QDebug>
#include <QPen>
#include "lxchartlegend.h"
#include "Algorithm/PeakFind/wh_peakSearch.h"
#include "nanoflann.hpp"
#include <QtCharts/QLineSeries>

QT_CHARTS_USE_NAMESPACE

#include <QtMath>
#include "FileData/taskmanager.h"
#include <QTime>
#include <QUuid>
#include <QLineF>
/**
 * @brief LxChartData类用于给LxChart控件提供数据
 */
class LxChartData : public QObject
{
    Q_OBJECT
public:
    static QMutex idMutex;             // 用于保护UniqueID的互斥锁
    static unsigned int LxChartDataId; // 用于生成唯一ID的静态成员变量

    /**
     * @brief 构造函数
     * @param parent 父对象 ParamPath 从属的FileData对象Param文件绝对路径
     */
    explicit LxChartData(QString ParamPath, GlobalEnums::TrackType trackType, GlobalEnums::IonMode ionMode = GlobalEnums::IonMode::NagativeIon,
                         GlobalEnums::ScanMode scanMode = GlobalEnums::ScanMode::MRM, QString dataProcess = "无处理", QString dataName = "默认数据名",
                         QString sampleName = "默认样本名", int eventNum = 0, QObject *parent = nullptr);

    /**
     * @brief 析构函数
     */
    ~LxChartData();

    /**
     * @brief 获取图表数据
     * @return 图表数据点集合
     */
    QVector<QPointF> getData() const;

    // 线程安全的数据操作方法 - 替代缓存结构体的功能
    void setDataThreadSafe(const QVector<double> &xData, const QVector<double> &yData);
    void appendDataPointThreadSafe(double x, double y);
    void clearDataThreadSafe();
    void setRangeThreadSafe(double minX, double maxX, double minY, double maxY);
    void resetRangeThreadSafe();
    QPair<QPair<double, double>, QPair<double, double>> getRangeThreadSafe() const; // ((minX, maxX), (minY, maxY))

    // 获取线程安全的数据副本
    QVector<double> getXDataThreadSafe() const;
    QVector<double> getYDataThreadSafe() const;
    QVector<QPointF> getDataThreadSafe() const;

    // 范围访问方法 - 替代直接访问私有成员
    double getMinX() const;
    double getMaxX() const;
    double getMinY() const;
    double getMaxY() const;
    bool hasValidRange() const; // 检查范围是否有效

    /**
     * @brief 设置图表数据（通用方法）
     * @param data 图表数据点集合
     */
    void setData(const QVector<QPointF> &data);

    /**
     * @brief 设置图表数据和范围
     * @param data 图表数据点集合
     * @param minX X轴最小值
     * @param maxX X轴最大值
     * @param minY Y轴最小值
     * @param maxY Y轴最大值
     */
    void setDataWithRange(const QVector<QPointF> &data, double minX, double maxX, double minY, double maxY);

    /**
     * @brief 设置原始数据x
     * @param x
     */
    void setDataX(const QVector<double> x);

    /**
     * @brief 设置原始数据y
     * @param y
     */
    void setDataY(const QVector<double> y);

    /**
     * @brief 获取原始数据x
     * @return
     */
    const QVector<double> getDataX();

    /**
     * @brief 获取原始数据y
     * @return
     */
    const QVector<double> getDataY();

    /**
     * @brief 获取图表标题
     * @return 图表标题
     */
    QString getTitle() const;

    /**
     * @brief 设置图表标题
     * @param title 图表标题
     */
    void setTitle(const QString &title);

    /**
     * @brief 获取X轴标签
     * @return X轴标签
     */
    QString getXAxisLabel() const;

    /**
     * @brief 设置X轴标签
     * @param label X轴标签
     */
    void setXAxisLabel(const QString &label);

    /**
     * @brief 获取X轴最小值
     * @return X轴最小值
     */
    double getXAxisMin() const;

    /**
     * @brief 设置X轴最小值
     * @param min X轴最小值
     */
    void setXAxisMin(double min);

    /**
     * @brief 获取X轴最大值
     * @return X轴最大值
     */
    double getXAxisMax() const;

    /**
     * @brief 设置X轴最大值
     * @param max X轴最大值
     */
    void setXAxisMax(double max);

    /**
     * @brief 设置X轴范围
     * @param min X轴最小值
     * @param max X轴最大值
     */
    void setXAxisRange(double min, double max);

    /**
     * @brief 设置Y轴范围
     * @param min Y轴最小值
     * @param max Y轴最大值
     */
    void setYAxisRange(double min, double max);

    /**
     * @brief 获取QLineSeries指针
     * @return QLineSeries指针
     */
    QLineSeries *getSeries() const { return m_series; }

    /**
     * @brief 设置QLineSeries指针
     * @param series QLineSeries指针
     */
    void setSeries(QLineSeries *series) { m_series = series; }

    /**
     * @brief 创建并设置新的QLineSeries
     * @param parent 父对象
     * @param name 系列名称
     * @return 创建的QLineSeries指针
     */
    QLineSeries *createSeries(QObject *parent = nullptr, const QString &name = "");

    /**
     * @brief 安全地删除QLineSeries
     */
    void deleteSeries();

    /**
     * @brief 获取Y轴标签
     * @return Y轴标签
     */
    QString getYAxisLabel() const;

    /**
     * @brief 设置Y轴标签
     * @param label Y轴标签
     */
    void setYAxisLabel(const QString &label);

    /**
     * @brief 获取背景区域
     * @return 背景区域（起点，终点）
     */
    QPair<double, double> getBackgroundArea() const;

    /**
     * @brief 设置背景区域
     * @param area 背景区域（起点，终点）
     */
    void setBackgroundArea(const QPair<double, double> &area);

    /**
     * @brief 获取标峰点
     * @return 标峰点集合
     */
    QVector<QPointF> getPeaks() const;

    /**
     * @brief 设置标峰点
     * @param peaks 标峰点集合
     */
    void setPeaks(const QVector<QPointF> &peaks);

    /**
     * @brief 获取曲线颜色
     * @return 曲线颜色
     */
    QColor getLineColor() const;

    /**
     * @brief 设置曲线颜色
     * @param color 曲线颜色
     */
    void setLineColor(const QColor &color);

    /**
     * @brief 获取曲线的原始颜色（用于高亮后恢复）
     * @return 原始曲线颜色
     */
    QColor getOriginalColor() const;

    /**
     * @brief 设置曲线的原始颜色（用于高亮后恢复）
     * @param color 原始曲线颜色
     */
    void setOriginalColor(const QColor &color);

    /**
     * @brief 获取从属的FileData的绝对路径（方便快速查找）
     * @return 从属的FileData的绝对路径
     */
    QString getParamPath();

    QString getUniqueID() const;

    // Legend ID管理
    QUuid getLegendId() const;
    void setLegendId(const QUuid &legendId);
    bool hasLegendId() const;

    /**
     * @brief 返回轨迹类型
     * @return
     */
    GlobalEnums::TrackType getTrackType();
    QPair<double, QPointF> getNearestPoint(QPointF point);

private:
    /**
     * @brief 初始化字符串参数
     */
    void initQStringParams();

public:
    LxChartLegend *legend = nullptr;
    bool m_bool_kdTreeEnabled = false;
    std::vector<Peak> peakVec;

    QPen getDefaultPen() const;
    void setDefaultPen(const QPen &newDefaultPen);

    int getEventNum() const;
    void setEventNum(int newEventNum);

    bool getHasFindPeak() const;
    void setHasFindPeak(bool newHasFindPeak);

    std::vector<Peak> getPeakVec() const;
    void setPeakVec(const std::vector<Peak> &newPeakVec);

private:
    void initKdTree(const QVector<QPointF> &vecPoint, double maxX, double minX, double maxY, double minY);
    GlobalDefine::PointCloud cloud;
    QMutex kdTreeMutex;
    using my_kd_tree_t = nanoflann::KDTreeSingleIndexAdaptor<nanoflann::L2_Simple_Adaptor<double, GlobalDefine::PointCloud>, GlobalDefine::PointCloud, 2>;
    my_kd_tree_t *kdtree = nullptr; // 使用指针存储 KD-Tree

    // QLineSeries指针，用于直接管理图表中的曲线对象
    QLineSeries *m_series = nullptr;

protected:
    // 子类需要访问的成员变量
    QString m_qstr_paramPath;       /// 从属FileData的唯一路径
    GlobalEnums::IonMode ionMode;   // 离子模式
    GlobalEnums::ScanMode scanMode; // 扫描模式
    QString dataProcess;            // 数据处理
    QString dataName;               // 数据名
    QString sampleName;             // 样品名

private:
    // 默认命名规则参数
    GlobalEnums::TrackType trackType; // 轨迹模式
    int eventNum;                     // 事件号
    QString UniqueID;                 // 唯一ID
    QUuid m_legendId;                 // 关联的LxChartLegend的ID

    // 线程安全的数据存储 - 替代原有的缓存结构体
    mutable QMutex m_dataMutex;
    QVector<double> m_xData; // 替代data_x，直接存储X数据
    QVector<double> m_yData; // 替代data_y，直接存储Y数据
    QVector<QPointF> m_data; // 保持兼容性，从m_xData和m_yData生成

    // 数据范围信息 - 替代结构体中的范围信息
    double m_maxX = -1, m_minX = -1, m_maxY = -1, m_minY = -1;

    QString m_qstr_title;      ///< 图表标题
    QString m_qstr_xAxisLabel; ///< x轴标签
    QString m_qstr_yAxisLabel; ///< y轴标签

    QPair<double, double> m_qpair_backgroundArea; ///< 背景区域
    QVector<QPointF> m_qvector_peaks;             ///< 标峰点
    QColor m_qcolor_lineColor;                    ///< 曲线颜色
    QColor m_qcolor_originalColor;                ///< 原始曲线颜色（用于高亮后恢复）

    QPen defaultPen;

    bool hasFindPeak = false;
};

#endif // LXCHARTDATA_H
