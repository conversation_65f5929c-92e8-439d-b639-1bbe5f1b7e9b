#include <QCoreApplication>
#include <QDebug>
#include <QFileInfo>
#include "LxDataReader/lxcustomdatareader.h"
#include "FileData/filedata.h"
#include "LxChart/ticchartdata.h"
#include "LxChart/xicchartdata.h"

/**
 * @brief 测试XIC数据修复
 *
 * 这个程序用于验证XIC数据是否能正确生成和设置范围
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    qDebug() << "=== XIC数据修复测试程序 ===";

    // 测试文件路径
    QString testFilePath = "C:/Users/<USER>/Desktop/新版数据格式20250717/20250724-抗生素-C1.Param";
    QFileInfo fileInfo(testFilePath);

    if (!fileInfo.exists())
    {
        qDebug() << "❌ 测试文件不存在:" << testFilePath;
        return -1;
    }

    qDebug() << "📁 使用测试文件:" << testFilePath;

    // 创建自定义读取器
    LxCustomDataReader *customReader = new LxCustomDataReader();
    qDebug() << "✅ LxCustomDataReader创建成功";

    // 创建FileData对象
    FileData *testData = new FileData(testFilePath);
    qDebug() << "✅ FileData对象创建成功";

    // 加载TIC数据
    qDebug() << "\n--- 加载TIC数据 ---";
    bool ticSuccess = customReader->loadTICDataComplete(testData);
    if (ticSuccess)
    {
        qDebug() << "✅ TIC数据加载成功";

        // 检查TIC数据
        auto ticDataMap = testData->getAllTicData();
        qDebug() << "TIC数据数量:" << ticDataMap.size();

        if (!ticDataMap.isEmpty())
        {
            // 获取第一个TIC数据
            int firstEventId = ticDataMap.firstKey();
            TicChartData *ticData = ticDataMap.first();

            qDebug() << "第一个TIC事件ID:" << firstEventId;
            qDebug() << "TIC数据点数:" << ticData->getData().size();
            qDebug() << "TIC X范围:" << ticData->getMinX() << "~" << ticData->getMaxX();
            qDebug() << "TIC Y范围:" << ticData->getMinY() << "~" << ticData->getMaxY();

            // 测试XIC数据生成
            qDebug() << "\n--- 测试XIC数据生成 ---";
            double testMz = 123.456;
            bool xicSuccess = customReader->loadXicDataBatch(testData, firstEventId, testMz);

            if (xicSuccess)
            {
                qDebug() << "✅ XIC数据生成成功";

                // 检查XIC数据
                if (ticData->hasXicData())
                {
                    QVector<XicChartData *> xicList = ticData->getXicDataList();
                    qDebug() << "XIC数据数量:" << xicList.size();

                    if (!xicList.isEmpty())
                    {
                        XicChartData *xicData = xicList.first();
                        qDebug() << "XIC标题:" << xicData->getTitle();
                        qDebug() << "XIC数据点数:" << xicData->getData().size();
                        qDebug() << "XIC X范围:" << xicData->getMinX() << "~" << xicData->getMaxX();
                        qDebug() << "XIC Y范围:" << xicData->getMinY() << "~" << xicData->getMaxY();

                        // 检查范围是否有效
                        if (xicData->getMinX() != -1 && xicData->getMaxX() != -1 &&
                            xicData->getMinY() != -1 && xicData->getMaxY() != -1)
                        {
                            qDebug() << "🎉 XIC数据范围设置正确！";
                        }
                        else
                        {
                            qDebug() << "❌ XIC数据范围无效";
                        }
                    }
                }
                else
                {
                    qDebug() << "❌ TIC中没有找到XIC数据";
                }
            }
            else
            {
                qDebug() << "❌ XIC数据生成失败";
            }
        }
    }
    else
    {
        qDebug() << "❌ TIC数据加载失败";
    }

    // 清理
    delete testData;
    delete customReader;

    qDebug() << "\n=== 测试完成 ===";

    return 0;
}
