#ifndef LXDATAREADER_H
#define LXDATAREADER_H

#include <QObject>
#include <QFile>
#include <QDebug>
#include <QDateTime>
// 移除DLL依赖的头文件
// #include "LibDataFileD/include/cDataFileRead.h"
#include "LibDataFilterR/include/cDataFilter.h"
#include "sMethod/cConfigOMS.h"
#include "method/cParamCCS.h"
#include "LibPeakAlgorithmR/include/cPeakAlgorithm.h"
#include "FileData/filedata.h"
#include <QtConcurrent>
#include <QtAlgorithms>
class LxDataReader : public QObject
{
    Q_OBJECT
public:
    explicit LxDataReader(QObject *parent = nullptr);
    // 整页读取Tic数据
    bool loadFileTicFullPage(FileData *data);
    bool loadFileMass(int index,                              // in<-帧数据位置
                      const std::vector<qint64> &pIndexArray, // in<-帧数据起始位置表
                      QByteArray &pDataY,                     // out->数据序列
                      QByteArray &pStreamBody,                // out->参数序列
                      QString pFilePath = QString()           // in<-完整文件路径，若为空，默认为应用程序当前路径+"/data/tmp.D"
    );

private:
    //*测试代码，随时可删除 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
    void qDebugStreamHead(_StreamHead *streamHead);
    void qDebugStreamHeadParam(cParamValue::_StreamHeadParam *param);
    void qDebugEvents(cParamValue::_Segment *pSegment);
    //*测试代码，随时可删除 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////

    int updateXIC(const QString &XicString);

    QString getNumXIC(const QByteArray &pStreamHead);
    QString getNumOtherLine(const QByteArray &pStreamHead);

    int getNumOtherLine(const QString &str, QList<std::vector<double>> &pStructLines);
    // bool oldFileStruct = true; // 当前处于老文件结构，还没有segNo eventNo experimentNo，所以只能当作一个文件一个experiment

    template <typename T>
    static void fillData(const QByteArray &pSrcData, QByteArray &pDstData, int countACC, int nFrameB, int nFrameE);

signals:
};

#endif // LXDATAREADER_H
