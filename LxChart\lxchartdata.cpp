#include "lxchartdata.h"
#include "FileData/filedata.h"
#include <QtCharts/QLineSeries>
#include <QtCharts/QChart>
// 初始化静态成员变量
QMutex LxChartData::idMutex;
unsigned int LxChartData::LxChartDataId = 0;

LxChartData::LxChartData(QString ParamPath, GlobalEnums::TrackType trackType, GlobalEnums::IonMode ionMode, GlobalEnums::ScanMode scanMode,
                         QString dataProcess, QString dataName, QString sampleName, int eventNum, QObject *parent)
    : QObject(parent), ionMode(ionMode), scanMode(scanMode), trackType(trackType), dataProcess(dataProcess), dataName(dataName), sampleName(sampleName),
      eventNum(eventNum), m_qstr_paramPath(ParamPath), m_qcolor_lineColor(Qt::blue)

{
    // 初始化背景区域
    m_qpair_backgroundArea = qMakePair(0.0, 0.0);
    // 生成唯一ID
    QMutexLocker locker(&idMutex);
    UniqueID = QUuid::createUuid().toString(QUuid::WithoutBraces);

    initQStringParams();
}

LxChartData::~LxChartData()
{
    qDebug() << "LxChartData::~LxChartData: 开始析构，UniqueID:" << UniqueID;

    // 安全删除QLineSeries（如果子类没有清理的话）
    try
    {
        if (m_series)
        {
            qDebug() << "LxChartData::~LxChartData: 基类清理QLineSeries";
            deleteSeries();
        }
        else
        {
            qDebug() << "LxChartData::~LxChartData: QLineSeries已被清理，跳过";
        }
    }
    catch (...)
    {
        qDebug() << "LxChartData::~LxChartData: 清理QLineSeries时发生异常";
    }

    // 注意：LxChartLegend的删除应该在图表删除时处理，而不是在这里
    // 因为在析构过程中删除LxChartLegend可能会导致虚函数调用问题
    if (legend)
    {
        qDebug() << "LxChartData::~LxChartData: 警告：LxChartLegend未在图表删除时清理，设置为nullptr";
        legend = nullptr; // 只设置为nullptr，不删除
    }

    qDebug() << "LxChartData::~LxChartData: 析构完成，UniqueID:" << UniqueID;
}

QVector<QPointF> LxChartData::getData() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_data;
}

// 线程安全的数据操作方法实现
void LxChartData::setDataThreadSafe(const QVector<double> &xData, const QVector<double> &yData)
{
    QMutexLocker locker(&m_dataMutex);
    m_xData = xData;
    m_yData = yData;

    // 更新m_data以保持兼容性
    m_data.clear();
    int minSize = qMin(m_xData.size(), m_yData.size());
    m_data.reserve(minSize);
    for (int i = 0; i < minSize; ++i)
    {
        m_data.append(QPointF(m_xData[i], m_yData[i]));
    }

    // 自动计算范围
    if (!m_xData.isEmpty() && !m_yData.isEmpty())
    {
        auto xMinMax = std::minmax_element(m_xData.begin(), m_xData.end());
        auto yMinMax = std::minmax_element(m_yData.begin(), m_yData.end());
        m_minX = *xMinMax.first;
        m_maxX = *xMinMax.second;
        m_minY = *yMinMax.first;
        m_maxY = *yMinMax.second;
    }
}

void LxChartData::appendDataPointThreadSafe(double x, double y)
{
    QMutexLocker locker(&m_dataMutex);
    m_xData.append(x);
    m_yData.append(y);
    m_data.append(QPointF(x, y));

    // 更新范围
    if (m_minX == -1 || x < m_minX)
        m_minX = x;
    if (m_maxX == -1 || x > m_maxX)
        m_maxX = x;
    if (m_minY == -1 || y < m_minY)
        m_minY = y;
    if (m_maxY == -1 || y > m_maxY)
        m_maxY = y;
}

void LxChartData::clearDataThreadSafe()
{
    QMutexLocker locker(&m_dataMutex);
    m_xData.clear();
    m_yData.clear();
    m_data.clear();
    resetRangeThreadSafe();
}

void LxChartData::setRangeThreadSafe(double minX, double maxX, double minY, double maxY)
{
    QMutexLocker locker(&m_dataMutex);
    m_minX = minX;
    m_maxX = maxX;
    m_minY = minY;
    m_maxY = maxY;
}

void LxChartData::resetRangeThreadSafe()
{
    // 注意：这个方法假设已经持有锁
    m_minX = m_maxX = m_minY = m_maxY = -1;
}

QPair<QPair<double, double>, QPair<double, double>> LxChartData::getRangeThreadSafe() const
{
    QMutexLocker locker(&m_dataMutex);
    return QPair<QPair<double, double>, QPair<double, double>>(
        QPair<double, double>(m_minX, m_maxX),
        QPair<double, double>(m_minY, m_maxY));
}

QVector<double> LxChartData::getXDataThreadSafe() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_xData;
}

QVector<double> LxChartData::getYDataThreadSafe() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_yData;
}

QVector<QPointF> LxChartData::getDataThreadSafe() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_data;
}

// 范围访问方法实现
double LxChartData::getMinX() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_minX;
}

double LxChartData::getMaxX() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_maxX;
}

double LxChartData::getMinY() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_minY;
}

double LxChartData::getMaxY() const
{
    QMutexLocker locker(&m_dataMutex);
    return m_maxY;
}

bool LxChartData::hasValidRange() const
{
    QMutexLocker locker(&m_dataMutex);
    // 检查基本有效性（不能是-1）
    if (m_minX == -1 || m_maxX == -1 || m_minY == -1 || m_maxY == -1)
    {
        return false;
    }

    // 允许单点数据（minX == maxX 或 minY == maxY）
    // 这种情况下图表会自动创建合适的显示范围
    return (m_minX <= m_maxX && m_minY <= m_maxY);
}

void LxChartData::setData(const QVector<QPointF> &data)
{
    QMutexLocker locker(&m_dataMutex);
    m_data = data;

    // 同时设置X和Y数据数组
    m_xData.clear();
    m_yData.clear();

    if (!data.isEmpty())
    {
        // 提取X和Y数据
        m_xData.reserve(data.size());
        m_yData.reserve(data.size());

        for (const QPointF &point : data)
        {
            m_xData.append(point.x());
            m_yData.append(point.y());
        }

        // 计算范围
        auto xRange = std::minmax_element(data.begin(), data.end(),
                                          [](const QPointF &a, const QPointF &b)
                                          { return a.x() < b.x(); });
        auto yRange = std::minmax_element(data.begin(), data.end(),
                                          [](const QPointF &a, const QPointF &b)
                                          { return a.y() < b.y(); });

        m_minX = xRange.first->x();
        m_maxX = xRange.second->x();
        m_minY = yRange.first->y();
        m_maxY = yRange.second->y();
    }
}

void LxChartData::setDataWithRange(const QVector<QPointF> &data, double minX, double maxX, double minY, double maxY)
{
    QMutexLocker locker(&m_dataMutex);
    m_data = data;
    m_minX = minX;
    m_maxX = maxX;
    m_minY = minY;
    m_maxY = maxY;

    // 同时设置X和Y数据数组
    m_xData.clear();
    m_yData.clear();

    if (!data.isEmpty())
    {
        m_xData.reserve(data.size());
        m_yData.reserve(data.size());

        for (const QPointF &point : data)
        {
            m_xData.append(point.x());
            m_yData.append(point.y());
        }
    }
}

void LxChartData::setXAxisRange(double min, double max)
{
    m_minX = min;
    m_maxX = max;
}

void LxChartData::setYAxisRange(double min, double max)
{
    m_minY = min;
    m_maxY = max;
}

QLineSeries *LxChartData::createSeries(QObject *parent, const QString &name)
{
    // 如果已有系列，先删除
    if (m_series)
    {
        deleteSeries();
    }

    // 创建新的系列
    m_series = new QLineSeries(parent);
    if (!name.isEmpty())
    {
        m_series->setName(name);
    }
    else
    {
        m_series->setName(getUniqueID());
    }

    // qDebug() << "创建QLineSeries，名称:" << m_series->name();
    return m_series;
}

void LxChartData::deleteSeries()
{
    if (m_series)
    {
        qDebug() << "LxChartData::deleteSeries: 删除QLineSeries，名称:" << m_series->name();

        // 断开所有信号连接，防止析构过程中的回调
        m_series->disconnect();

        // 从图表中移除（如果还在图表中）
        if (m_series->chart())
        {
            m_series->chart()->removeSeries(m_series);
            qDebug() << "LxChartData::deleteSeries: 从图表中移除QLineSeries";
        }

        // 立即删除而不是使用deleteLater，避免事件循环中的虚函数调用
        delete m_series;
        m_series = nullptr;
        qDebug() << "LxChartData::deleteSeries: QLineSeries删除完成";
    }
}

void LxChartData::setDataX(const QVector<double> x)
{
    QMutexLocker locker(&m_dataMutex);
    m_xData = x;
}

void LxChartData::setDataY(const QVector<double> y)
{
    QMutexLocker locker(&m_dataMutex);
    m_yData = y;
}

const QVector<double> LxChartData::getDataX()
{
    QMutexLocker locker(&m_dataMutex);
    return m_xData;
}

const QVector<double> LxChartData::getDataY()
{
    QMutexLocker locker(&m_dataMutex);
    return m_yData;
}
QString LxChartData::getTitle() const
{
    return m_qstr_title;
}

void LxChartData::setTitle(const QString &title)
{
    m_qstr_title = title;
}

QString LxChartData::getXAxisLabel() const
{
    return m_qstr_xAxisLabel;
}

void LxChartData::setXAxisLabel(const QString &label)
{
    m_qstr_xAxisLabel = label;
}

QString LxChartData::getYAxisLabel() const
{
    return m_qstr_yAxisLabel;
}

void LxChartData::setYAxisLabel(const QString &label)
{
    m_qstr_yAxisLabel = label;
}

QPair<double, double> LxChartData::getBackgroundArea() const
{
    return m_qpair_backgroundArea;
}

void LxChartData::setBackgroundArea(const QPair<double, double> &area)
{
    m_qpair_backgroundArea = area;
}

QVector<QPointF> LxChartData::getPeaks() const
{
    return m_qvector_peaks;
}

void LxChartData::setPeaks(const QVector<QPointF> &peaks)
{
    m_qvector_peaks = peaks;
}

QColor LxChartData::getLineColor() const
{
    return m_qcolor_lineColor;
}

void LxChartData::setLineColor(const QColor &color)
{
    m_qcolor_lineColor = color;
}

QString LxChartData::getParamPath()
{
    return m_qstr_paramPath;
}

QString LxChartData::getUniqueID() const
{
    return UniqueID;
}

GlobalEnums::TrackType LxChartData::getTrackType()
{
    return trackType;
}

QPair<double, QPointF> LxChartData::getNearestPoint(QPointF point)
{
    QMutexLocker locker(&kdTreeMutex);
    if (!m_bool_kdTreeEnabled || !kdtree)
        return QPair(-1, QPointF(0, 0));

    // 把查询点转成归一化的 double 数组
    double query_pt[2] = {(point.x() - cloud.minX) / (cloud.maxX - cloud.minX), (point.y() - cloud.minY) / (cloud.maxY - cloud.minY)};
    qDebug() << "查询归一化目标点:" << query_pt[0] << query_pt[1];
    // 确保归一化结果在合理范围内
    if (query_pt[0] < 0)
        query_pt[0] = 0;
    if (query_pt[0] > 1)
        query_pt[0] = 1;
    if (query_pt[1] < 0)
        query_pt[1] = 0;
    if (query_pt[1] > 1)
        query_pt[1] = 1;

    // 用 knnSearch，找到 K 个最近邻点
    const size_t num_results = 1; // 增加到3，找到更多候选点
    size_t index[num_results];
    double dist_sqr[num_results];
    size_t returnedResults = kdtree->knnSearch(query_pt, num_results, index, dist_sqr);

    // 如果没有找到任何结果，返回错误值
    if (returnedResults == 0)
    {
        return QPair(-1, QPointF(0, 0));
    }

    // 找到最接近的点
    QPointF nearest = m_data.at(index[0]);

    // 计算原始坐标系中的实际距离
    double actualDistance = QLineF(point, nearest).length();

    qDebug() << "查询点" << point << "最近点" << nearest << "距离:" << actualDistance;

    return QPair(actualDistance, nearest);
}

void LxChartData::initQStringParams()
{
    // 初始化图表标题
    // 离子模式
    switch (ionMode)
    {
    case GlobalEnums::IonMode::NagativeIon:
        m_qstr_title += tr("负离子");
        break;
    case GlobalEnums::IonMode::PositiveIon:
        m_qstr_title += tr("正离子");
        break;
    default:
        break;
    }
    m_qstr_title += "-";
    // 扫描模式
    switch (scanMode)
    {
    case GlobalEnums::ScanMode::FullScan:
        m_qstr_title += tr("全扫描");
        break;
    case GlobalEnums::ScanMode::MRM:
        m_qstr_title += tr("MRM");
        break;
    case GlobalEnums::ScanMode::NeutralLossScan:
        m_qstr_title += tr("中性丢失");
        break;
    case GlobalEnums::ScanMode::ParentIonScan:
        m_qstr_title += tr("母离子扫描");
        break;
    case GlobalEnums::ScanMode::ProductIonScan:
        m_qstr_title += tr("子离子扫描");
        break;

    default:
        break;
    }
    m_qstr_title += "-";

    switch (trackType)
    {
    case GlobalEnums::TrackType::TIC:
        m_qstr_xAxisLabel = tr("时间");
        m_qstr_yAxisLabel = tr("强度");

        m_qstr_title += tr("TIC");
        break;
    case GlobalEnums::TrackType::XIC:
        m_qstr_xAxisLabel = tr("时间");
        m_qstr_yAxisLabel = tr("强度");
        m_qstr_title += tr("XIC");

        break;
    case GlobalEnums::TrackType::MS:
        m_qstr_title += tr("MS");

        m_qstr_xAxisLabel = tr("m/z");
        m_qstr_yAxisLabel = tr("强度");
        break;
    case GlobalEnums::TrackType::TWC:
        m_qstr_title += tr("TWC");
        break;
    case GlobalEnums::TrackType::XWC:
        m_qstr_title += tr("XWC");
        break;
    case GlobalEnums::TrackType::DAD:
        m_qstr_title += tr("DAD");
        break;

    default:
        break;
    }
    m_qstr_title += "-";

    m_qstr_title += dataProcess;
    m_qstr_title += "@";

    m_qstr_title += dataName;
    m_qstr_title += "/";

    m_qstr_title += sampleName;
    m_qstr_title += "/";

    m_qstr_title += QString::number(eventNum);

    qDebug() << "标题名:" << m_qstr_title;
}

void LxChartData::initKdTree(const QVector<QPointF> &vecPoint, double maxX, double minX, double maxY, double minY)
{
    // qDebug() << "initKdTree线程" << QThread::currentThread();
    TaskManager::instance()->runNamed("初始化KdTree", [=]()
                                      {
        // qDebug() << "初始化KDTRee线程" << QThread::currentThread();
        QTime time;
        time.start();
        QMutexLocker locker(&kdTreeMutex);
        cloud.points = vecPoint.toStdVector();

        // 确保minX < maxX和minY < maxY，防止数值错误
        cloud.maxX = qMax(maxX, minX);
        cloud.minX = qMin(maxX, minX);
        cloud.maxY = qMax(maxY, minY);
        cloud.minY = qMin(maxY, minY);

        for (int i = 0; i < cloud.points.size(); i++) {
            double x = cloud.points[i].x();
            double y = cloud.points[i].y();

            double xNormal = (x - cloud.minX) / (cloud.maxX - cloud.minX);
            double yNormal = (y - cloud.minY) / (cloud.maxY - cloud.minY);

            QPointF normalP(xNormal, yNormal);

            cloud.points[i] = normalP;
        }
        qDebug() << "归一化后的点:";
        for (int i = 0; i < cloud.points.size(); i++) {
            qDebug() << cloud.points.at(i);
        }

        // 如果最大值和最小值相等，添加一个小偏移量防止除以零
        if (qFuzzyCompare(cloud.maxX, cloud.minX)) {
            cloud.maxX += 1.0;
        }
        if (qFuzzyCompare(cloud.maxY, cloud.minY)) {
            cloud.maxY += 1.0;
        }

        m_bool_kdTreeEnabled = false;

        // 确保点集不为空
        if (cloud.points.empty()) {
            qDebug() << "警告: KD-Tree初始化的点集为空";
            return;
        }

        if (kdtree != nullptr) {
            // qDebug() << "开始delete";
            delete kdtree;
            // qDebug() << "delete完成";
            kdtree = nullptr;
        }

        // 创建新的KD-Tree并构建索引
        kdtree = new my_kd_tree_t(2, cloud, nanoflann::KDTreeSingleIndexAdaptorParams(10)); // 10是树的分支因子
        kdtree->buildIndex();
        m_bool_kdTreeEnabled = true;
        qDebug() << "KD-Tree初始化完成，点数:" << cloud.points.size() << "耗时:" << time.elapsed() << "ms";
        qDebug() << "数据范围 X:" << cloud.minX << "-" << cloud.maxX << " Y:" << cloud.minY << "-" << cloud.maxY; });
}

bool LxChartData::getHasFindPeak() const
{
    return hasFindPeak;
}

void LxChartData::setHasFindPeak(bool newHasFindPeak)
{
    hasFindPeak = newHasFindPeak;
}

int LxChartData::getEventNum() const
{
    return eventNum;
}

void LxChartData::setEventNum(int newEventNum)
{
    eventNum = newEventNum;
}

QPen LxChartData::getDefaultPen() const
{
    return defaultPen;
}

void LxChartData::setDefaultPen(const QPen &newDefaultPen)
{
    defaultPen = newDefaultPen;
}

/**
 * @brief 获取曲线的原始颜色
 * @return 原始曲线颜色
 */
QColor LxChartData::getOriginalColor() const
{
    return m_qcolor_originalColor;
}

/**
 * @brief 设置曲线的原始颜色
 * @param color 原始曲线颜色
 */
void LxChartData::setOriginalColor(const QColor &color)
{
    m_qcolor_originalColor = color;
}

/**
 * @brief 获取关联的LxChartLegend的ID
 * @return Legend ID
 */
QUuid LxChartData::getLegendId() const
{
    return m_legendId;
}

/**
 * @brief 设置关联的LxChartLegend的ID
 * @param legendId Legend ID
 */
void LxChartData::setLegendId(const QUuid &legendId)
{
    m_legendId = legendId;
}

/**
 * @brief 检查是否有关联的Legend ID
 * @return 是否有有效的Legend ID
 */
bool LxChartData::hasLegendId() const
{
    return !m_legendId.isNull();
}
