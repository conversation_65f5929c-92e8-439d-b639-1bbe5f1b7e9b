#ifndef GLOBALENUMS_H
#define GLOBALENUMS_H

// 不需要特意include这个.h，只需要include GlobalDefine.h 足够，内部自动#include此类了

//*下拉框枚举 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
namespace GlobalEnums
{

    enum class ComboBox_Enums_ProcessMethod
    {
        // ProcessMethod枚举
        New,
        Open,
        Delete,
        Eidt,
        SaveAs,
        Locked,
        Unlocked,
        DefaultSettings
    };

    enum class ComboBox_Enums_Views
    {
        // Views枚举
        ResultTableList,
        ReakReview,
        CalibrationCurve,
        AuditTrailReview,
        ShowHiddenPane
    };

    enum class ComboBox_Enums_Results
    {
        // Results枚举
        New,
        Open,
        Delete,
        Reprecess,
        Save,
        SaveAs,
        LockSelectedResults,
        LockResultSetAndSave
    };

    enum class ComboBox_Enums_Report
    {
        // Report枚举
        Open,
        Create,
        Delete,
        ExportAsMetric,
        ExportAsTableList,
        ExportSelectedAsMetric,
        ExportSelectedAsTableList,
        LIMSTransferSetting,
        TransferResultsToLIMS
    };

    enum class ComboBox_Enums_Edit
    {
        // Edit枚举
        AddColumns,
        RenameColumns,
        RemoveColumns,
        HideRows,
        ShowRows,
        RemoveSelectedRows
    };

    enum class ComboBox_Enums_PeakReviewSettings
    {
        // PeakReviewSettings枚举
        Show,
        Apperance,
        Zooming
    };

    enum class ComboBox_Enums_More
    {
        CopyHighlight,
        CopyAll,
        ExportAll
    };
    //*下拉框枚举 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////

    //*项目默认参数设置 */////////////⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️⬇️///////////////
    // 噪音计算方法枚举
    enum class Noise_Caculation
    {
        Standrad, // 标准偏差
        Peak      // 峰峰比
    };
    // 峰切割方法枚举
    enum class Peak_Spliting_Method
    {
        Vertical, // 强迫垂直线
        Gaussian  // 高斯切割
    };
    // 基线类型枚举
    enum class Baseline_Type
    {
        Multi, // 多项式拟合曲线
        P2P    // 点到点
    };

    // 校准曲线坐标轴处理方法枚举
    enum class Standard_Curve_Asix
    {
        Log,
        Ln
    };

    // 回归参数
    // 面积，高度，面积比，高度比；前两者为外标法，后两者为内标法，若选择内标法，则必须指定内标，存在未指定的情况应在点击保存时弹出消息提示
    enum class Regression_Paramenters
    {
        Area,    // 面积
        Hight,   // 高度
        AreaDiv, // 面积比
        HightDiv // 高度比
    };
    // 回归次数
    enum class Regression_Type
    {
        One,
        Two,
        Three,
        Four,
        Five
    };

    // 加权类型 x，1/x，1/x^2，y，1/y， 1/y^2，x^2，logx，lnx；“对数”默认底数为10
    enum class Weighting_Type
    {
        X,
        Div_X,
        Div_X_2,
        Y,
        Div_Y,
        Div_Y_2,
        X_2,
        log_X,
        ln_X
    };
    //*项目默认参数设置 */////////////⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️⬆️///////////////

    // LxChart交互模式枚举
    enum class InteractionMode
    {
        Mode_None,    // 无交互
        Mode_Pan,     // 平移
        Mode_Select,  // 点选
        Mode_ZoomRect // 框选缩放
    };

    // 平均质谱计算状态
    enum class AvgMassStatus
    {
        Calculating, // 计算中
        Ready,       // 计算完成
        Stop         // 停止计算
    };

    /**
     * @brief 离子模式
     */
    enum class IonMode
    {
        PositiveIon, // 正离子
        NagativeIon  // 负离子
    };
    /**
     * @brief 扫描模式
     */
    enum class ScanMode
    {
        FullScan,        // 全扫描
        NeutralLossScan, // 中性丢失扫描
        ParentIonScan,   // 母离子扫描
        ProductIonScan,  // 子离子扫描
        MRM              // MRM扫描
    };

    /**
     * @brief 轨迹类型
     */
    enum class TrackType
    {
        TIC,
        XIC,
        TWC,
        XWC,
        MS,
        DAD
    };
} // namespace GlobalEnums
#endif // GLOBALENUMS_H
