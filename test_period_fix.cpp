#include <QCoreApplication>
#include <QDebug>
#include <QFileInfo>
#include <QSettings>
#include <QDir>

/**
 * @brief 专门测试Period限制修复的简化程序
 * 
 * 这个程序用于验证：
 * 1. 创建system.ini配置文件，设置大的Period值
 * 2. 对比修改前后的数据读取结果
 * 3. 验证是否解决了数据截断问题
 */

void createSystemIniFile()
{
    qDebug() << "=== 创建system.ini配置文件 ===";
    
    QString appDir = QCoreApplication::applicationDirPath();
    QString configPath = appDir + "/system.ini";
    
    qDebug() << "配置文件路径:" << configPath;
    
    QSettings settings(configPath, QSettings::IniFormat);
    
    // 设置大的Period值（1000天）
    settings.setValue("Analysis/Period", 86400000);
    settings.setValue("Analysis/maxiHeighMassChart", 16777215);
    
    settings.sync();
    
    if (QFileInfo(configPath).exists()) {
        qDebug() << "✅ system.ini文件创建成功";
        qDebug() << "📄 Period设置为: 86400000 (1000天)";
    } else {
        qDebug() << "❌ system.ini文件创建失败";
    }
}

void displayFileInfo(const QString &filePath)
{
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        qDebug() << "❌ 文件不存在:" << filePath;
        return;
    }
    
    qDebug() << "=== 文件信息 ===";
    qDebug() << "文件路径:" << filePath;
    qDebug() << "文件大小:" << fileInfo.size() << "字节";
    qDebug() << "文件大小:" << QString::number(fileInfo.size() / 1024.0 / 1024.0, 'f', 2) << "MB";
    qDebug() << "修改时间:" << fileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss");
}

void checkCurrentConfig()
{
    qDebug() << "=== 检查当前配置 ===";
    
    QString appDir = QCoreApplication::applicationDirPath();
    QString configPath = appDir + "/system.ini";
    
    if (QFileInfo(configPath).exists()) {
        QSettings settings(configPath, QSettings::IniFormat);
        double period = settings.value("Analysis/Period", 86400).toDouble();
        
        qDebug() << "✅ 找到system.ini配置文件";
        qDebug() << "📊 当前Period值:" << period;
        qDebug() << "📊 Period天数:" << QString::number(period / 86400.0, 'f', 1) << "天";
        
        if (period >= 86400000) {
            qDebug() << "✅ Period值已设置为大值，应该能解决数据截断问题";
        } else {
            qDebug() << "⚠️ Period值较小，可能仍存在数据截断问题";
        }
    } else {
        qDebug() << "❌ 未找到system.ini配置文件";
        qDebug() << "💡 将使用默认Period值: 86400秒 (24小时)";
    }
}

void showUsageInstructions()
{
    qDebug() << "\n=== 使用说明 ===";
    qDebug() << "1. 本程序已创建system.ini配置文件，设置Period为1000天";
    qDebug() << "2. 现在可以运行主程序测试大文件读取：";
    qDebug() << "   HZHDataReader.exe";
    qDebug() << "3. 打开问题文件：";
    qDebug() << "   C:/Users/<USER>/Desktop/新版数据格式20250717/20250724-抗生素-C1.Param";
    qDebug() << "4. 观察是否还出现数据截断问题";
    qDebug() << "\n=== 预期结果 ===";
    qDebug() << "✅ 如果修复成功：应该能读取完整的数据，不再出现截断";
    qDebug() << "❌ 如果仍有问题：可能需要进一步调试或使用自定义读取器";
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== Period限制修复测试程序 ===";
    qDebug() << "目标：解决大文件读取时的数据截断问题\n";
    
    // 显示测试文件信息
    QString testFilePath = "C:/Users/<USER>/Desktop/新版数据格式20250717/20250724-抗生素-C1.Param";
    displayFileInfo(testFilePath);
    
    // 检查当前配置
    checkCurrentConfig();
    
    // 创建新的配置文件
    createSystemIniFile();
    
    // 再次检查配置
    qDebug() << "\n=== 配置更新后检查 ===";
    checkCurrentConfig();
    
    // 显示使用说明
    showUsageInstructions();
    
    qDebug() << "\n=== 程序完成 ===";
    
    return 0;
}
