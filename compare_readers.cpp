#include <QCoreApplication>
#include <QDebug>
#include <QFileInfo>
#include <QElapsedTimer>
#include "LxDataReader/lxcustomdatareader.h"
// 移除DataReader DLL依赖
// #include "FileData/datareader.h"
#include "FileData/filedata.h"

/**
 * @brief 对比测试原始DLL读取器和自定义读取器
 *
 * 这个程序用于对比验证：
 * 1. 原始DataReader（使用DataFile.dll，有Period限制）
 * 2. 新的LxCustomDataReader（无Period限制）
 *
 * 重点验证大文件的数据完整性问题
 */

void testOriginalReader(const QString &filePath)
{
    qDebug() << "\n=== 测试原始DataReader（DLL版本） ===";

    DataReader *originalReader = new DataReader();
    FileData *testData = new FileData(filePath);

    QElapsedTimer timer;
    timer.start();

    bool success = originalReader->loadTICData(*testData);
    qint64 elapsedTime = timer.elapsed();

    if (success)
    {
        qDebug() << "✅ 原始读取器成功";
        qDebug() << "⏱️ 读取耗时:" << elapsedTime << "毫秒";

        // 检查数据完整性
        qDebug() << "📊 TIC X数据点数:" << testData->dataTIC_X.size();
        qDebug() << "📊 TIC Y数据点数:" << testData->dataTIC_Y.size();
        qDebug() << "🗂️ 索引数组大小:" << testData->indexArray.size();
        qDebug() << "📄 分页数量:" << testData->pageTIC.size();

        if (!testData->dataTIC_X.empty())
        {
            qDebug() << "   时间范围:" << testData->dataTIC_X.front() << "~" << testData->dataTIC_X.back();
            double timeSpan = testData->dataTIC_X.back() - testData->dataTIC_X.front();
            qDebug() << "   总时间跨度:" << timeSpan << "秒 (" << timeSpan / 3600.0 << "小时)";
        }

        // 检查是否被分页截断
        if (testData->pageTIC.size() > 1)
        {
            qDebug() << "⚠️ 检测到分页机制，可能存在数据截断";
            qDebug() << "   分页信息:";
            for (int i = 0; i < testData->pageTIC.size(); ++i)
            {
                qDebug() << "     页" << i << "起始位置:" << testData->pageTIC[i];
            }
        }
    }
    else
    {
        qDebug() << "❌ 原始读取器失败";
        qDebug() << "⏱️ 失败耗时:" << elapsedTime << "毫秒";
    }

    delete testData;
    delete originalReader;
}

void testCustomReader(const QString &filePath)
{
    qDebug() << "\n=== 测试自定义LxCustomDataReader（无Period限制） ===";

    LxCustomDataReader *customReader = new LxCustomDataReader();
    FileData *testData = new FileData(filePath);

    QElapsedTimer timer;
    timer.start();

    bool success = customReader->loadTICDataComplete(testData);
    qint64 elapsedTime = timer.elapsed();

    if (success)
    {
        qDebug() << "✅ 自定义读取器成功";
        qDebug() << "⏱️ 读取耗时:" << elapsedTime << "毫秒";

        // 检查数据完整性
        QList<int> eventIds = testData->getAllEventIds();
        qDebug() << "📊 事件数量:" << eventIds.size();

        int totalTicPoints = 0;
        double minTime = std::numeric_limits<double>::max();
        double maxTime = std::numeric_limits<double>::min();

        for (int eventId : eventIds)
        {
            TicChartData *ticData = testData->getTicData(eventId);
            if (ticData)
            {
                QVector<QPointF> ticPoints = ticData->getDataThreadSafe();
                totalTicPoints += ticPoints.size();

                if (!ticPoints.isEmpty())
                {
                    minTime = qMin(minTime, ticPoints.first().x());
                    maxTime = qMax(maxTime, ticPoints.last().x());
                }
            }
        }

        qDebug() << "📊 总TIC数据点数:" << totalTicPoints;
        qDebug() << "🗂️ 索引数组大小:" << testData->getIndexArray().size();

        if (totalTicPoints > 0)
        {
            qDebug() << "   时间范围:" << minTime << "~" << maxTime;
            double timeSpan = maxTime - minTime;
            qDebug() << "   总时间跨度:" << timeSpan << "秒 (" << timeSpan / 3600.0 << "小时)";
        }

        qDebug() << "✨ 无分页限制，读取完整数据";
    }
    else
    {
        qDebug() << "❌ 自定义读取器失败";
        qDebug() << "⏱️ 失败耗时:" << elapsedTime << "毫秒";
    }

    delete testData;
    delete customReader;
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    qDebug() << "=== 数据读取器对比测试 ===";

    // 测试文件路径
    QString testFilePath = "C:/Users/<USER>/Desktop/新版数据格式20250717/20250724-抗生素-C1.Param";

    // 检查文件是否存在
    QFileInfo fileInfo(testFilePath);
    if (!fileInfo.exists())
    {
        qDebug() << "测试文件不存在:" << testFilePath;
        return -1;
    }

    qDebug() << "测试文件:" << testFilePath;
    qDebug() << "文件大小:" << fileInfo.size() << "字节 (" << fileInfo.size() / 1024.0 / 1024.0 << "MB)";

    // 测试原始读取器
    testOriginalReader(testFilePath);

    // 测试自定义读取器
    testCustomReader(testFilePath);

    qDebug() << "\n=== 对比测试完成 ===";
    qDebug() << "如果自定义读取器的数据点数明显多于原始读取器，";
    qDebug() << "则说明成功解决了Period限制导致的数据截断问题！";

    return 0;
}
