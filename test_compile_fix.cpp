#include <QCoreApplication>
#include <QDebug>
#include "FileData/avgmassmanager.h"
#include "LxDataReader/lxcustomdatareader.h"

/**
 * @brief 测试编译修复
 * 
 * 这个程序用于验证所有编译错误是否已修复
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== 编译修复测试程序 ===";
    
    // 测试AvgMassManager
    qDebug() << "✅ AvgMassManager编译成功";
    qDebug() << "当前平均质谱状态:" << static_cast<int>(AvgMassManager::getAvgMassStatus());
    
    // 测试LxCustomDataReader
    LxCustomDataReader *reader = new LxCustomDataReader();
    qDebug() << "✅ LxCustomDataReader编译成功";
    delete reader;
    
    qDebug() << "🎉 所有编译错误已修复！";
    
    return 0;
}
