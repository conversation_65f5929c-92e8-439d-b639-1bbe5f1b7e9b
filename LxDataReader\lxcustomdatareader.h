#ifndef LXCUSTOMDATAREADER_H
#define LXCUSTOMDATAREADER_H

#include <QObject>
#include <QFile>
#include <QDebug>
#include <QDateTime>
#include <QByteArray>
#include <QVector>
#include <QList>
#include <QMap>
#include <vector>
#include "FileData/filedata.h"

/**
 * @brief 自定义数据读取器 - 不依赖DataFile.dll
 *
 * 这个类完全重新实现了数据文件读取功能，解决了Period限制导致的数据截断问题
 * 主要特点：
 * 1. 不依赖任何外部DLL
 * 2. 完整读取所有数据，无Period限制
 * 3. 支持大文件处理
 * 4. 内存优化的数据读取
 */
class LxCustomDataReader : public QObject
{
    Q_OBJECT

public:
    explicit LxCustomDataReader(QObject *parent = nullptr);
    ~LxCustomDataReader();

    // 主要接口函数
    bool loadTICDataComplete(FileData *data);
    bool loadMassData(int index, const std::vector<qint64> &indexArray,
                      QByteArray &dataY, QByteArray &streamBody,
                      const QString &filePath);

    // 新增：完整的MASS数据加载和设置到FileData
    bool loadMassDataComplete(FileData *data, int frameIndex, int eventId);

    // 新增：从StreamHead中解析Segment信息
    bool parseSegmentFromStreamHead(const QByteArray &streamHead, FileData *data);

    // 新增：从Segment获取m/z范围（用于XIC和MASS）
    QPair<double, double> getMzRangeFromSegment(FileData *data, int eventId);

    // 新增：批量加载XIC数据
    bool loadXicDataBatch(FileData *data, int eventId, double mz);

    // 新增：加载平均质谱数据
    bool loadMassDataForAvg(int eventId, const QVector<int> &frameIndexVec, FileData *data);

private:
    // 数据结构定义（从cPublicCCS.h复制关键结构）
    struct StreamHead
    {
        enum TypeParam
        {
            Type_Null = 0,
            Type_Tuning = 1,
            Type_Acquisition = 2,
            Type_Tuning_RGA = 3,
            Type_Moniter_RGA = 4,
            Type_VacuumDiagnostics_RGA = 5,
            Type_LeakCheck_RGA = 6
        };

        quint32 length = 0;
        TypeParam typeParam = Type_Null;
        quint32 dateTime = 0;
        char param[0];
    };

    struct StreamHeadParam
    {
        enum TypeChildParam : quint32
        {
            Type_Child_Null = 0,
            Type_Segment_Param = 1,
            Type_Method_Param = 2,
            Type_Process_Param = 3,
            Type_XIC_Param = 4,
            Type_Tuning_Param = 5,
            Type_XIC_Select = 6,
            Type_Property_Str = 7,
            Type_Line_Param = 8
        };

        quint32 length = 0;
        TypeChildParam type = Type_Child_Null;
        char param[0];
    };

    struct StreamBody
    {
        enum TypeData : quint32
        {
            Type_Null = 0,
            Type_Uint16Compress = 1,
            Type_Uint16 = 2,
            Type_FloatCompress = 3,
            Type_DoubleCompress = 4,
            Type_Double = 5,
            Type_Uint8Compress = 6,
            Type_Float = 7,
            Type_Uint8 = 8,
            Type_Uint32Compress = 9,
            Type_Uint32 = 10,
            Type_Error = 9999
        };

        quint32 length = 0;
        TypeData typeParam = Type_Null;
        quint32 dateTime = 0;
        quint32 lengthParam = 0;
        char param[0];
    };

    // XIC参数结构
    struct XICParam
    {
        double massRange = 0.0;
        quint32 color = 0;
        double gain = 1.0;
        double offset = 0.0;
        std::vector<double> yListXIC;

        XICParam(double range = 0.0, quint32 col = 0, double g = 1.0, double off = 0.0)
            : massRange(range), color(col), gain(g), offset(off) {}
    };

    // 核心读取函数
    bool readFileHeader(const QString &filePath, QByteArray &streamHead);
    bool parseStreamHead(const QByteArray &streamHead,
                         int &numXIC, int &numOtherLine,
                         QMap<quint32, QMap<QString, XICParam *>> &xicMap);

    bool readAllTICData(const QString &filePath, const QByteArray &streamHead,
                        int numXIC, int numOtherLine,
                        std::vector<qint64> &indexArray,
                        std::vector<double> &ticX,
                        std::vector<double> &ticY,
                        QMap<quint32, QMap<QString, XICParam *>> &xicMap,
                        QList<std::vector<double>> &structLines);

    bool readMassDataBody(const QString &filePath,
                          const std::vector<qint64> &indexArray,
                          int frameBegin, int frameEnd,
                          QByteArray &dataY, QByteArray &streamBody);

    // 解析质谱数据
    bool parseMassData(const QByteArray &dataY, const QByteArray &streamBody,
                       QVector<double> &massX, QVector<double> &massY);

    // 从StreamBody中解析m/z范围
    QPair<double, double> parseMzRangeFromStreamBody(const QByteArray &streamBody);

    // 辅助函数
    QString extractXICString(const QByteArray &streamHead);
    QString extractOtherLineString(const QByteArray &streamHead);
    int parseXICString(const QString &xicString,
                       QMap<quint32, QMap<QString, XICParam *>> &xicMap);
    int parseOtherLineString(const QString &lineString,
                             QList<std::vector<double>> &structLines);

    // 数据类型转换模板
    template <typename T>
    void convertDataType(const QByteArray &srcData, QByteArray &dstData,
                         int countACC, int frameBegin, int frameEnd);

    // 模板函数需要在头文件中实现
    template <typename T>
    void convertDataTypeImpl(const QByteArray &srcData, QByteArray &dstData,
                             int countACC, int frameBegin, int frameEnd);

    // 调试函数
    void debugStreamHead(const StreamHead *streamHead);
    void debugStreamHeadParam(const StreamHeadParam *param);

    // 成员变量
    bool m_debugMode = true;
};

#endif // LXCUSTOMDATAREADER_H
