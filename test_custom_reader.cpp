#include <QCoreApplication>
#include <QDebug>
#include <QFileInfo>
#include <QElapsedTimer>
#include "LxDataReader/lxcustomdatareader.h"
#include "FileData/filedata.h"

/**
 * @brief 测试自定义数据读取器的功能
 * 
 * 这个测试程序用于验证LxCustomDataReader是否能够：
 * 1. 完整读取大文件数据（无Period限制）
 * 2. 正确解析文件头和数据结构
 * 3. 成功提取TIC、XIC和MASS数据
 */
int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "=== LxCustomDataReader 测试程序 ===";
    
    // 测试文件路径（使用问题文件）
    QString testFilePath = "C:/Users/<USER>/Desktop/新版数据格式20250717/20250724-抗生素-C1.Param";
    
    // 检查文件是否存在
    QFileInfo fileInfo(testFilePath);
    if (!fileInfo.exists()) {
        qDebug() << "测试文件不存在:" << testFilePath;
        qDebug() << "请确保文件路径正确";
        return -1;
    }
    
    qDebug() << "测试文件:" << testFilePath;
    qDebug() << "文件大小:" << fileInfo.size() << "字节";
    
    // 创建自定义数据读取器
    LxCustomDataReader *customReader = new LxCustomDataReader();
    
    // 创建FileData对象
    FileData *testData = new FileData(testFilePath);
    
    qDebug() << "\n=== 开始测试数据读取 ===";
    
    QElapsedTimer timer;
    timer.start();
    
    // 测试TIC数据读取
    bool success = customReader->loadTICDataComplete(testData);
    
    qint64 elapsedTime = timer.elapsed();
    
    if (success) {
        qDebug() << "✅ TIC数据读取成功！";
        qDebug() << "⏱️ 读取耗时:" << elapsedTime << "毫秒";
        
        // 检查TIC数据
        QList<int> eventIds = testData->getAllEventIds();
        qDebug() << "📊 事件数量:" << eventIds.size();
        
        for (int eventId : eventIds) {
            TicChartData *ticData = testData->getTicData(eventId);
            if (ticData) {
                QVector<QPointF> ticPoints = ticData->getDataThreadSafe();
                qDebug() << "📈 事件" << eventId << "TIC数据点数:" << ticPoints.size();
                
                if (!ticPoints.isEmpty()) {
                    qDebug() << "   时间范围:" << ticPoints.first().x() << "~" << ticPoints.last().x();
                    qDebug() << "   强度范围:" << "最小值待计算" << "~" << "最大值待计算";
                }
                
                // 检查XIC数据
                QList<XicChartData*> xicDataList = ticData->getXicDataList();
                if (!xicDataList.isEmpty()) {
                    qDebug() << "🔬 XIC数据数量:" << xicDataList.size();
                    for (XicChartData *xicData : xicDataList) {
                        QVector<QPointF> xicPoints = xicData->getDataThreadSafe();
                        qDebug() << "   XIC m/z:" << xicData->getMz() << ", 数据点数:" << xicPoints.size();
                    }
                }
            }
        }
        
        // 测试索引数组
        const std::vector<qint64> &indexArray = testData->getIndexArray();
        qDebug() << "🗂️ 索引数组大小:" << indexArray.size();
        
        if (!indexArray.empty()) {
            qDebug() << "   第一个索引:" << indexArray.front();
            qDebug() << "   最后一个索引:" << indexArray.back();
        }
        
        // 测试MASS数据读取（读取第一帧）
        if (!indexArray.empty()) {
            qDebug() << "\n=== 测试MASS数据读取 ===";
            
            QByteArray massData, streamBody;
            bool massSuccess = customReader->loadMassData(0, indexArray, massData, streamBody, testFilePath);
            
            if (massSuccess) {
                qDebug() << "✅ MASS数据读取成功！";
                qDebug() << "📊 MASS数据大小:" << massData.size() << "字节";
                qDebug() << "📋 StreamBody大小:" << streamBody.size() << "字节";
                
                // 计算质谱数据点数（假设是double类型）
                int massPointCount = massData.size() / sizeof(double);
                qDebug() << "🔬 质谱数据点数:" << massPointCount;
            } else {
                qDebug() << "❌ MASS数据读取失败";
            }
        }
        
    } else {
        qDebug() << "❌ TIC数据读取失败！";
        qDebug() << "⏱️ 失败耗时:" << elapsedTime << "毫秒";
    }
    
    qDebug() << "\n=== 测试完成 ===";
    
    // 清理资源
    delete testData;
    delete customReader;
    
    return success ? 0 : -1;
}
