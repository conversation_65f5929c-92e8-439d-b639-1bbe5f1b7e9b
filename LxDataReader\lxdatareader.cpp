#include "lxdatareader.h"

LxDataReader::LxDataReader(QObject *parent) : QObject{parent}
{
}

bool LxDataReader::loadFileTicFullPage(FileData *data)
{
    QString pFilePath = data->getFilePath();
    if (pFilePath.isEmpty())
    {
        return false;
    }
    QList<std::vector<double>> pStructLines;

    // 先加载struct _StreamHead
    QByteArray pStreamHead;

    _StreamHead *tmpStreamHead;
    if (pFilePath.isEmpty())
        return false;
    qint64 sizeHead = sizeof(_StreamHead);
    QFile tmpFile(pFilePath);
    pStreamHead.resize(sizeHead);
    try
    {
        if (!tmpFile.open(QIODevice::ReadOnly))
            return false;
        tmpFile.seek(0);
    }
    catch (...)
    {
        return false;
    }
    try
    {
        if (sizeHead != tmpFile.read(pStreamHead.data(), sizeHead))
        {
            tmpFile.close();
            return false;
        }
        else
        {
            tmpStreamHead = (_StreamHead *)(pStreamHead.data());
            sizeHead = tmpStreamHead->length;
            pStreamHead.resize(sizeHead);
            tmpStreamHead = (_StreamHead *)(pStreamHead.data());
            tmpFile.seek(0);
            if (sizeHead != tmpFile.read(pStreamHead.data(), sizeHead))
            {
                tmpFile.close();
                return false;
            }
        }
    }
    catch (...)
    {
        return false;
    }
    // qDebugStreamHead(tmpStreamHead);
    // QList<cParamValue::_StreamHeadParam *> tmpList;
    // if (!_StreamHead::toList(pStreamHead, tmpList)) {
    //     return false;
    // }
    // foreach (cParamValue::_StreamHeadParam *param, tmpList) {
    //     qDebugStreamHeadParam(param);
    // }
    // qDebug().noquote() << "子属性长度：" << sizeof(tmpList) << tmpList.length();

    int numXIC = updateXIC(getNumXIC(pStreamHead));
    int numOtherLine = getNumOtherLine(getNumOtherLine(pStreamHead), pStructLines);
    // 暂时不启用这个代码，因为文件结构还没变，等到otherLine里有segNo eventNo experimentNo在启用
    if (false)
    {
        if (numOtherLine != 3)
        {
            // 如果numOtherLine小于3 则表示没有 segNo eventNo experimentNo的信息，读取失败
            qDebug() << "numOtherLine小于3，读取失败";
            return false;
        }
    }
    qint64 sizeData = tmpFile.size() - sizeHead;
    int sizeI64 = sizeof(qint64), sizeD = sizeof(double);
    if (sizeData <= 0)
    {
        // 如果数据部分大小小于等于0，清空所有输出参数并返回false
        qDebug() << "数据大小为0 读取失败";
        return false;
    }
    // 计算各数据偏移量：索引偏移、Y值偏移、XIC偏移和其他线偏移
    int offsetI = sizeI64 + sizeD * 2 + sizeD * numXIC + sizeD * numOtherLine;
    int offsetY = sizeI64 + sizeD;
    int offsetXIC = sizeI64 + sizeD + sizeD;
    int offsetOtherLine = offsetXIC + numXIC * sizeD;

    // cursor: 计算数据行数（总数据大小除以每行数据的大小）
    qint64 lineData = sizeData / (offsetI);
    QByteArray tempArray;
    tempArray.resize(offsetI * 3);
    try
    {
        if (offsetI * 3 != tmpFile.read(tempArray.data(), offsetI * 3))
        {
            tmpFile.close();
            return false;
        }
    }
    catch (...)
    {
        tmpFile.close();
        return false;
    }
    double interval = *((double *)(tempArray.data() + offsetI * 2 + sizeI64)) - *((double *)(tempArray.data() + offsetI + sizeI64));
    double allTime = ((double)sizeData) / ((double)offsetI) * interval;

    // 调整临时数组大小以适应所有数据
    tempArray.resize(sizeData);
    try
    {
        // cursor: 将文件指针定位到头部结束位置（数据起始位置）
        tmpFile.seek(sizeHead);
        // cursor: 尝试读取所有数据
        if (sizeData != tmpFile.read(tempArray.data(), sizeData))
        {
            // cursor: 如果读取的字节数不等于预期大小，关闭文件并返回false
            tmpFile.close();
            return false;
        }
    }
    catch (...)
    {
        // cursor: 捕获异常但不做处理（可能应该添加错误处理）
    }

    try
    {
        // cursor: 尝试关闭文件
        tmpFile.close();
    }
    catch (...)
    {
        // cursor: 捕获关闭文件时的异常，返回false
        return false;
    }
    // cursor: 初始化行偏移量为0
    int offsetLine = 0;

    // cursor: 设置每条结构线的大小为数据行数
    for (int j = 0; j < numOtherLine; ++j)
    {
        pStructLines[j].resize(lineData);
    }
    QVector<qint64> pIndexArray;
    for (qint64 i = 0; i < lineData; ++i)
    {
        // cursor: 计算当前行在字节数组中的偏移量
        offsetLine = i * offsetI;
        // cursor: 先遍历每条结构线，将数据从临时数组复制到结构线数组

        // 获取  segNo eventNo experimentNo
        if (true)
        {
            double ticX;
            double ticY;
            double experimentNo = 0.0; // 因为没有修改文件结构，所以默认只有一个experiment
            // 使用新的数据结构，移除Experiment概念
            // 直接使用TicChartData管理数据
            int eventId = static_cast<int>(experimentNo);
            TicChartData *ticData = data->getTicData(eventId);
            if (!ticData)
            {
                ticData = data->createTicData(eventId);
                qDebug() << "LxDataReader: 创建TIC数据，事件ID:" << eventId;
            }
            // TODO: 重构LxDataReader以使用新的TicChartData结构
            // 暂时注释掉旧的Experiment数据处理逻辑
            qDebug() << "LxDataReader: 需要重构数据处理逻辑以使用新的TicChartData结构";

            // 临时跳过数据处理，避免编译错误
            // 这部分需要重新设计以适配新的数据结构
        }
        else
        {
            // TODO: 重构LxDataReader的else分支以使用新的TicChartData结构
            qDebug() << "LxDataReader: else分支需要重构，暂时跳过";
            // 暂时注释掉，需要重构
        }

        // TODO: 重构这部分数据处理逻辑
        // 暂时注释掉所有experiment相关的代码
    }

    // TODO: 重构数据初始化逻辑以使用新的TicChartData结构
    // 暂时注释掉旧的ExperimentMap和TicData处理逻辑
    qDebug() << "LxDataReader: 数据初始化逻辑需要重构，暂时跳过";
    // 注释掉残留的ticdata和experiment处理代码
    qDebug().noquote() << "加载完成" << numXIC << numOtherLine;
}

bool LxDataReader::loadFileMass(int index, const std::vector<qint64> &pIndexArray, QByteArray &pDataY, QByteArray &pStreamBody, QString pFilePath)
{
    // cursor: 检查文件路径是否为空，如果为空则返回false
    if (pFilePath.isEmpty())
        return false;

    int nFrameB = index;
    int nFrameE = index;
    if ((pIndexArray.size() <= nFrameB) || (pIndexArray.size() <= nFrameE))
        return false;
    if (!pFilePath.isEmpty())
    {
        QFile tmpDataFileMass(pFilePath);
        try
        {
            if (!tmpDataFileMass.open(QIODevice::ReadOnly))
                return false;
            tmpDataFileMass.seek(0);
        }
        catch (...)
        {
            return false;
        }
        int tmpACC = 0;
        QByteArray uncompressBuff;
        do
        {
            qint64 offset = pIndexArray[nFrameB + tmpACC];
            qint64 sizeBody = 0;
            qint64 sizeStreamBody = sizeof(_StreamBody);
            if (offset >= tmpDataFileMass.size())
                return false;
            pStreamBody.resize(sizeStreamBody);
            try
            {
                if (!tmpDataFileMass.seek(offset))
                {
                    tmpDataFileMass.close();
                    return false;
                }
                if (sizeStreamBody != tmpDataFileMass.read(pStreamBody.data(), sizeStreamBody))
                {
                    tmpDataFileMass.close();
                    return false;
                }
                _StreamBody *tmpStreamBody = (_StreamBody *)(pStreamBody.data());
                if (tmpStreamBody->lengthParam > 0)
                {
                    ulong lengthParam = tmpStreamBody->lengthParam;
                    pStreamBody.resize(sizeStreamBody + lengthParam);
                    tmpDataFileMass.read(pStreamBody.data() + sizeStreamBody, lengthParam);
                    tmpStreamBody = (_StreamBody *)(pStreamBody.data());
                }
                sizeBody = tmpStreamBody->length - sizeStreamBody - tmpStreamBody->lengthParam;
                QByteArray tmpArray;
                tmpArray.resize(sizeBody);
                if (sizeBody != tmpDataFileMass.read(tmpArray.data(), sizeBody))
                {
                    tmpDataFileMass.close();
                    return false;
                }
                switch (tmpStreamBody->typeParam)
                {
                case _StreamBody::Type_Uint16Compress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<quint16>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint16:
                    fillData<quint16>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_FloatCompress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<float>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Float:
                    fillData<float>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_DoubleCompress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<double>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Double:
                    fillData<double>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint8Compress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<quint8>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint8:
                    fillData<quint8>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint32Compress:
                    uncompressBuff = qUncompress(tmpArray);
                    fillData<quint32>(uncompressBuff, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                case _StreamBody::Type_Uint32:
                    fillData<quint32>(tmpArray, pDataY, tmpACC, nFrameB, nFrameE);
                    break;
                default:
                    break;
                }
            }
            catch (...)
            {
                return false;
            }
        } while (++tmpACC + nFrameB <= nFrameE);
        return true;
    }
    return false;
}

void LxDataReader::qDebugStreamHead(_StreamHead *streamHead)
{
    qDebug().noquote() << "头长度:" << streamHead->length;
    QDateTime dt = QDateTime::fromSecsSinceEpoch(streamHead->dateTime, Qt::UTC);
    qDebug().noquote() << "本地时间：" << streamHead->dateTime << dt.toLocalTime().toString("yyyy-MM-dd hh:mm:ss");
    QString baseType;
    switch (streamHead->typeParam)
    {
    case _StreamHead::Type_Param::Type_Null:
    {
        baseType = "Type_Null";
        break;
    };
    case _StreamHead::Type_Param::Type_Tuning:
    {
        baseType = "Type_Tuning";
        break;
    };
    case _StreamHead::Type_Param::Type_Acquisition:
    {
        baseType = "Type_Acquisition";
        break;
    };
    case _StreamHead::Type_Param::Type_Tuning_RGA:
    {
        baseType = "Type_Tuning_RGA";
        break;
    };
    case _StreamHead::Type_Param::Type_Moniter_RGA:
    {
        baseType = "Type_Moniter_RGA";
        break;
    };
    case _StreamHead::Type_Param::Type_VacuumDiagnostics_RGA:
    {
        baseType = "Type_VacuumDiagnostics_RGA";
        break;
    };
    case _StreamHead::Type_Param::Type_LeakCheck_RGA:
    {
        baseType = "Type_LeakCheck_RGA";
        break;
    }
    }
    qDebug().noquote() << "基础类型:" + baseType;
}

void LxDataReader::qDebugStreamHeadParam(cParamValue::_StreamHeadParam *param)
{
    qDebug().noquote() << "   cParamValue::_StreamHeadParam长度:" << param->length;
    QString _StreamHeadParamType;
    switch (param->type)
    {
    case cParamValue::Type_Child_Param::Type_Child_Null:
    {
        _StreamHeadParamType = "Type_Child_Null";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Segment_Param /*二进制参数编码*/:
    {
        _StreamHeadParamType = "Type_Segment_Param";
        // 读取Segment
        QByteArray tmpArray;
        int dataSize = param->length - sizeof(cParamValue::_StreamHeadParam);
        tmpArray.resize(dataSize);

        // 从param中的数据部分开始复制
        char *dataStart = reinterpret_cast<char *>(param) + sizeof(cParamValue::_StreamHeadParam);
        memcpy(tmpArray.data(), dataStart, dataSize);

        // 如果您确实需要指针，请确保QByteArray在整个使用期间保持有效
        cParamValue::_Segment *tmpSegment = reinterpret_cast<cParamValue::_Segment *>(tmpArray.data());
        QString segType;
        // 使用tmpSegment
        switch (tmpSegment->type)
        {
        case cParamValue::Type_Segment::Type_Seg_Null:
        {
            segType = "Type_Seg_Null";
        }
        case cParamValue::Type_Segment::Type_LIT_TARGET:
        {
            segType = "Type_LIT_TARGET";
        }
        case cParamValue::Type_Segment::Type_LIT_2019:
        {
            segType = "Type_LIT_2019";
        }
        case cParamValue::Type_Segment::Type_LIT_FULL:
        {
            segType = "Type_LIT_FULL";
        }
        case cParamValue::Type_Segment::Type_Seg_RGA:
        {
            segType = "Type_Seg_RGA";
        }
        case cParamValue::Type_Segment::Type_Seg_TripleQ:
        {
            segType = "Type_Seg_TripleQ";
        }
        }
        qDebug().noquote() << "         Segment Type:" + segType;

        qDebug() << "segment length:" << tmpSegment->length;
        qDebug() << "segment countsEvent:" << tmpSegment->countsEvent;
        qDebug() << "segment lengthEvent:" << tmpSegment->lengthEvent;
        qDebug() << "segment lengthReserved:" << tmpSegment->lengthReserved;
        qDebug() << "segment time:" << tmpSegment->getSegTimeMs(tmpSegment);
        // ...
        qDebugEvents(tmpSegment);
        break;
    }
    case cParamValue::Type_Child_Param::Type_Method_Param /*字符串参数编码*/:
    {
        _StreamHeadParamType = "Type_Method_Param";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Process_Param /*二进制处理方法编码暂未启用*/:
    {
        _StreamHeadParamType = "Type_Process_Param";
        break;
    }
    case cParamValue::Type_Child_Param::Type_XIC_Param /*字符串XIC方法编码*/:
    {
        _StreamHeadParamType = "Type_XIC_Param";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Tuning_Param /*调谐文件内容*/:
    {
        _StreamHeadParamType = "Type_Tuning_Param";
        break;
    }
    case cParamValue::Type_Child_Param::Type_XIC_Select /*字符串XIC方法编码*/:
    {
        _StreamHeadParamType = "Type_XIC_Select";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Property_Str /*字符串属性property1&value1;property2&value2*/:
    {
        _StreamHeadParamType = "Type_Property_Str";
        break;
    }
    case cParamValue::Type_Child_Param::Type_Line_Param /*除了TIC、XIC外的绘图数据绘图数据长度==TIC*/:
    {
        _StreamHeadParamType = "Type_Line_Param";
        break;
    }
    }

    qDebug().noquote() << "   -------_StreamHeadParam类型:" + _StreamHeadParamType;
}
template <typename T>
void LxDataReader::fillData(const QByteArray &pSrcData, QByteArray &pDstData, int countACC, int nFrameB, int nFrameE)
{
    qint64 sizeData = pSrcData.size() / sizeof(T);
    const T *pSrcY = reinterpret_cast<const T *>(pSrcData.data());
    if (countACC == 0)
    {
        pDstData.resize(sizeData * sizeof(double));
        double *pDstY = (double *)pDstData.data();
        for (int uIndex = 0; uIndex < sizeData; ++uIndex)
        {
            pDstY[uIndex] = (double)(pSrcY[uIndex]);
        }
    }
    else if (countACC + nFrameB == nFrameE)
    {
        double *pDstY = (double *)pDstData.data();
        for (int uIndex = 0; uIndex < sizeData; ++uIndex)
        {
            pDstY[uIndex] = (double)(countACC - 1) / (double)(countACC)*pDstY[uIndex] + (double)(pSrcY[uIndex]) / (double)(countACC);
            pDstY[uIndex] = pDstY[uIndex]; // pDstY[uIndex]= pDstY[uIndex]* dbRawScale- 4.197;
        }
    }
    else
    {
        double *pDstY = (double *)pDstData.data();
        for (int uIndex = 0; uIndex < sizeData; ++uIndex)
        {
            pDstY[uIndex] = (double)(countACC - 1) / (double)(countACC)*pDstY[uIndex] + (double)(pSrcY[uIndex]) / (double)(countACC);
        }
    }
}
void LxDataReader::qDebugEvents(cParamValue::_Segment *pSegment)
{
    int offsetP = 0;

    for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; ++currentEvt)
    {
        cParamValue::_Event *pEvent = (cParamValue::_Event *)((char *)&(pSegment->fisrtEvent) + offsetP + pSegment->lengthReserved);
        QString eventType;
        qDebug() << "holdTimeMs:" << pEvent->holdTimeMs;
        qDebug() << "delayTimeMs:" << pEvent->delayTimeMs;
        qDebug() << "preReadyTimeMs:" << pEvent->preReadyTimeMs;
        qDebug() << "postReadyTimeMs:" << pEvent->postReadyTimeMs;
        qDebug() << "minTimeMs:" << pEvent->minTimeMs;
        qDebug() << "msPrecursor:" << pEvent->msPrecursor;
        qDebug() << "title:" << pEvent->title;
        qDebug() << "lengthReserved:" << pEvent->lengthReserved;
        if (cParamValue::Type_SIM == pEvent->type)
        {
            eventType = "_EventSIM";
            qDebug() << "eventType:" << eventType;
            cParamValue::_EventSIM *eventSIM = static_cast<cParamValue::_EventSIM *>(pEvent);
            qDebug() << "eventSIM length:" << eventSIM->length();
            qDebug() << "eventSIM size:" << eventSIM->size();

            for (uint i = 0; i < CHANNEL_COUNTS_MAX; i++)
            {
                qDebug() << eventSIM->mass[i] << "," << eventSIM->massOrig[i] << "," << eventSIM->timeMs[i];
            }

            offsetP += sizeof(cParamValue::_EventSIM);
        }
        else if (cParamValue::Type_Scan_RGA == pEvent->type)
        {
            eventType = "_EventScanRGA";
            qDebug() << "eventType:" << eventType;
            offsetP += sizeof(cParamValue::_EventScanRGA);

            cParamValue::_EventScanRGA *eventScanRGA = static_cast<cParamValue::_EventScanRGA *>(pEvent);

            qDebug() << "dwell_time:" << eventScanRGA->dwell_time;
            qDebug() << "points_per_amu:" << eventScanRGA->points_per_amu;
            qDebug() << "mass_begin:" << eventScanRGA->mass_begin;
            qDebug() << "mass_end:" << eventScanRGA->mass_end;
        }
        else
        {
            eventType = "_EventLIT";
            qDebug() << "eventType:" << eventType;
            offsetP += sizeof(cParamValue::_EventLIT);
            cParamValue::_EventLIT *eventLIT = static_cast<cParamValue::_EventLIT *>(pEvent);

            qDebug() << eventLIT->msStart << "," << eventLIT->msEnd << "," << eventLIT->msStartOrig << "," << eventLIT->msEndOrig;
        }
    }
}

int LxDataReader::getNumOtherLine(const QString &str, QList<std::vector<double>> &pStructLines)
{
    pStructLines.clear();
    if (str.isEmpty())
        return 0;
    int counts = 0;
    QStringList lstStr = str.split(';');
    foreach (auto tmpStr, lstStr)
    {
        if (tmpStr.isEmpty())
            continue;
        QStringList tmpList = tmpStr.split('&');
        if (tmpList.size() < 2)
            continue;
        int tmpLines = tmpList[1].toInt();
        if (tmpLines < 1)
            continue;
        counts += tmpLines;
        //        QList<std::vector<double>> structList;
        //        for(int i=0; i<tmpLines; ++i){
        //            structList<< std::vector<double>(0);
        //        }
        pStructLines << std::vector<double>(0);
    }
    return counts;
}

QString LxDataReader::getNumOtherLine(const QByteArray &pStreamHead)
{
    QString LineString;
    QList<cParamValue::_StreamHeadParam *> tmpList;
    if (!_StreamHead::toList(pStreamHead, tmpList))
        return QString();
    for (int i = 0; i < tmpList.size(); ++i)
    {
        switch (tmpList[i]->type)
        {
        case cParamValue::Type_Line_Param:
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            LineString = QString::fromUtf8(tmpArray);
            //            QStringList lstStr = XicString.split('/');
            //            if(lstStr.size()< 2)
            //                return QString();
            //            if(lstStr[1].isEmpty())
            //                return QString();
            return LineString;
        }
        default:
            break;
        }
    }
    return QString();
}

int LxDataReader::updateXIC(const QString &XicString)
{
    QStringList lstStr = XicString.split('/');
    if (lstStr.size() < 2)
        return 0;
    QStringList lstCurves = lstStr[1].split('@');
    return lstCurves.size();
}
QString LxDataReader::getNumXIC(const QByteArray &pStreamHead)
{
    QString XicString;
    QList<cParamValue::_StreamHeadParam *> tmpList;
    if (!_StreamHead::toList(pStreamHead, tmpList))
        return QString();
    for (int i = 0; i < tmpList.size(); ++i)
    {
        switch (tmpList[i]->type)
        {
        case cParamValue::Type_XIC_Param:
        {
            QByteArray tmpArray;
            tmpArray.resize(tmpList[i]->length - sizeof(cParamValue::_StreamHeadParam));
            memcpy(tmpArray.data(), tmpList[i]->param, tmpArray.size());
            XicString = QString::fromUtf8(tmpArray);
            QStringList lstStr = XicString.split('/');
            if (lstStr.size() < 2)
                return QString();
            if (lstStr[1].isEmpty())
                return QString();
            return XicString;
        }
        default:
            break;
        }
    }
    return QString();
}
