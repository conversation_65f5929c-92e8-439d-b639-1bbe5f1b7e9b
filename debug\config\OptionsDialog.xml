<?xml version='1.0' encoding='UTF-8'?>
<root version="1.0" software="HZH_OS">
 <class name="PeakFindingChromatograms">
  <property name="FiledType">
   <value>0</value>
  </property>
  <property name="PeakThreshold">
   <value>5</value>
  </property>
  <property name="NoisePercent">
   <value>30</value>
  </property>
  <property name="PeakSplit">
   <value>1</value>
  </property>
  <property name="Fitting">
   <value>3</value>
  </property>
  <property name="Diff">
   <value>5</value>
  </property>
 </class>
 <class name="PeakFindingMass">
  <property name="FiledType">
   <value>0</value>
  </property>
  <property name="PeakThreshold">
   <value>5</value>
  </property>
  <property name="CentroidHeightPercentage">
   <value>50</value>
  </property>
 </class>
 <class name="PeakFindingDAD">
  <property name="FiledType">
   <value>0</value>
  </property>
  <property name="PeakThreshold">
   <value>5</value>
  </property>
  <property name="NoisePercent">
   <value>30</value>
  </property>
  <property name="PeakSplit">
   <value>1</value>
  </property>
  <property name="Fitting">
   <value>3</value>
  </property>
  <property name="Diff">
   <value>5</value>
  </property>
 </class>
 <class name="AppearanceSettings">
  <property name="axisLabelFontFamily">
   <value>Microsoft YaHei</value>
  </property>
  <property name="axisLabelFontSize">
   <value>9</value>
  </property>
  <property name="axisLabelFontBold">
   <value>false</value>
  </property>
  <property name="axisLabelFontItalic">
   <value>false</value>
  </property>
  <property name="axisLabelFontStrikeOut">
   <value>false</value>
  </property>
  <property name="axisLabelFontUnderline">
   <value>false</value>
  </property>
  <property name="autoLabelFontFamily">
   <value>Microsoft YaHei</value>
  </property>
  <property name="autoLabelFontSize">
   <value>9</value>
  </property>
  <property name="autoLabelFontBold">
   <value>false</value>
  </property>
  <property name="autoLabelFontItalic">
   <value>false</value>
  </property>
  <property name="autoLabelFontStrikeOut">
   <value>false</value>
  </property>
  <property name="autoLabelFontUnderline">
   <value>false</value>
  </property>
  <property name="customLabelFontFamily">
   <value>Microsoft YaHei</value>
  </property>
  <property name="customLabelFontSize">
   <value>20</value>
  </property>
  <property name="customLabelFontBold">
   <value>false</value>
  </property>
  <property name="customLabelFontItalic">
   <value>false</value>
  </property>
  <property name="customLabelFontStrikeOut">
   <value>true</value>
  </property>
  <property name="customLabelFontUnderline">
   <value>true</value>
  </property>
  <property name="axisTitleFontFamily">
   <value>Microsoft YaHei</value>
  </property>
  <property name="axisTitleFontSize">
   <value>10</value>
  </property>
  <property name="axisTitleFontBold">
   <value>true</value>
  </property>
  <property name="axisTitleFontItalic">
   <value>false</value>
  </property>
  <property name="axisTitleFontStrikeOut">
   <value>false</value>
  </property>
  <property name="axisTitleFontUnderline">
   <value>false</value>
  </property>
  <property name="titleFontFamily">
   <value>Microsoft YaHei</value>
  </property>
  <property name="titleFontSize">
   <value>12</value>
  </property>
  <property name="titleFontBold">
   <value>true</value>
  </property>
  <property name="titleFontItalic">
   <value>false</value>
  </property>
  <property name="titleFontStrikeOut">
   <value>false</value>
  </property>
  <property name="titleFontUnderline">
   <value>false</value>
  </property>
  <property name="spectrumLineWidth">
   <value>1</value>
  </property>
 </class>
 <class name="PeakFindingParameters">
  <property name="useDefault">
   <value>false</value>
  </property>
  <property name="smoothType">
   <value>1</value>
  </property>
  <property name="windowSize">
   <value>3</value>
  </property>
  <property name="gaussianSigma">
   <value>1.5</value>
  </property>
  <property name="polyOrder">
   <value>1</value>
  </property>
  <property name="baselineType">
   <value>1</value>
  </property>
  <property name="alsLambda">
   <value>1.000000e+05</value>
  </property>
  <property name="alsP">
   <value>0.01</value>
  </property>
  <property name="alsMaxIter">
   <value>5</value>
  </property>
  <property name="noiseWindow">
   <value>10</value>
  </property>
  <property name="minPeakWidth">
   <value>2</value>
  </property>
  <property name="slopeFactor">
   <value>1</value>
  </property>
  <property name="minPeakArea">
   <value>0</value>
  </property>
  <property name="minPeakHeight">
   <value>0</value>
  </property>
  <property name="folderWidthOfNoise">
   <value>1</value>
  </property>
 </class>
</root>
