#include "mrmreader.h"
#include "datareader.h"
#include <QDebug>
#include <QMutexLocker>

MRMReader::MRMReader(QObject *parent) : QObject(parent)
{
}

MRMReader::~MRMReader()
{
}

// 获取当前扫描类型
GlobalDefine::ScanMode MRMReader::getCurrentScanMode(FileData &data)
{
    // 获取第一个Segment
    DataReader dataReader;
    cParamValue::_Segment *pSegment = dataReader.getSegment(0, data);
    if (!pSegment) {
        qDebug() << "获取扫描类型失败：无法获取Segment";
        return GlobalDefine::ScanMode::FullScan; // 默认返回全扫描模式
    }
    
    // 解析扫描类型
    // 这里需要根据实际情况判断扫描类型，暂时根据事件类型简单判断
    // 如果需要更精确的判断，需要根据Segment中的详细参数来判断
    if (pSegment->countsEvent > 0) {
        // 获取第一个事件的类型
        cParamValue::_Event *pEvent = (cParamValue::_Event *)&(pSegment->fisrtEvent);
        
        // 根据事件类型判断扫描模式
        switch(pEvent->type) {
            case cParamValue::Type_SIM:
            case cParamValue::Type_SIM_2048:
                return GlobalDefine::ScanMode::MRM; // SIM类型通常对应MRM模式
            case cParamValue::Type_Scan_RGA:
                return GlobalDefine::ScanMode::FullScan; // RGA类型通常对应全扫描模式
            default:
                // 其他类型根据实际情况判断
                // 可能需要进一步分析事件参数
                return GlobalDefine::ScanMode::FullScan;
        }
    }
    
    // 默认返回全扫描模式
    return GlobalDefine::ScanMode::FullScan;
}

// 判断是否为MRM数据
bool MRMReader::isMRMData(FileData &data)
{
    // 调用获取扫描类型的函数，判断是否为MRM类型
    return getCurrentScanMode(data) == GlobalDefine::ScanMode::MRM;
}

// 获取MRM列表数据
QVector<StructMRM> MRMReader::getMRMDataList(FileData &data)
{
    // 清空现有的MRM数据列表
    Struct_MRM_Vec.clear();
    
    // 检查是否为MRM数据
    if (!isMRMData(data)) {
        qDebug() << "当前数据不是MRM数据，无法获取MRM列表";
        return Struct_MRM_Vec;
    }
    
    // 获取第一个Segment
    DataReader dataReader;
    cParamValue::_Segment *pSegment = dataReader.getSegment(0, data);
    if (!pSegment) {
        qDebug() << "获取MRM列表失败：无法获取Segment";
        return Struct_MRM_Vec;
    }
    
    // 处理每个事件，提取MRM数据
    int offsetP = 0;
    for (uint32_t currentEvt = 0; currentEvt < pSegment->countsEvent; ++currentEvt) {
        cParamValue::_Event *pEvent = (cParamValue::_Event *)((char *)&(pSegment->fisrtEvent) + offsetP);
        
        // 根据事件类型处理
        if (pEvent->type == cParamValue::Type_SIM || pEvent->type == cParamValue::Type_SIM_2048) {
            cParamValue::_EventSIM *pEventSIM = (cParamValue::_EventSIM *)pEvent;
            
            // 创建MRM数据结构
            StructMRM mrmData;
            mrmData.Experiment = currentEvt + 1; // 从1开始的实验编号
            mrmData.ID = QString("MRM%1").arg(currentEvt + 1); // 默认ID
            mrmData.Q1 = pEventSIM->mass_parent; // 母离子质荷比
            mrmData.Q3 = pEventSIM->mass_daughter; // 子离子质荷比
            mrmData.RT = pEventSIM->holdTimeMs / 1000.0; // 保留时间(秒)
            
            // 添加到列表
            Struct_MRM_Vec.append(mrmData);
            
            // 更新偏移量
            offsetP += sizeof(cParamValue::_EventSIM);
        } else {
            // 非SIM类型事件，更新偏移量并跳过
            offsetP += sizeof(cParamValue::_EventLIT);
        }
    }
    
    qDebug() << "成功获取MRM列表，共" << Struct_MRM_Vec.size() << "条记录";
    return Struct_MRM_Vec;
}
